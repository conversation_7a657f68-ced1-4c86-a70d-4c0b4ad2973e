<?php /*

  This file is part of a child theme called Waw Theme Child.
  Functions in this file will be loaded before the parent theme's functions.
  For more information, please read
  https://developer.wordpress.org/themes/advanced-topics/child-themes/

*/

require_once get_template_directory() . '/app/bootstrap.php';
require_once get_stylesheet_directory() . '/app/bootstrap.php';

function child_styles()
{
	// Build dependencies array starting with theme styles
	$dependencies = ['waw-theme-styles'];

	// Only check for Gravity Forms styles if the plugin is active
	if (class_exists('GFForms')) {
		// Add Gravity Forms styles as dependencies if they're registered
		$gravity_styles = [
			'gforms_reset_css',
			'gform_basic',
			'gforms_formsmain_css',
			'gforms_ready_class_css',
			'gforms_browsers_css',
			'gform_theme',
			'gravity_forms_theme_reset',
			'gravity_forms_theme_foundation',
			'gravity_forms_theme_framework',
			'gravity_forms_orbital_theme'
		];

		foreach ($gravity_styles as $style_handle) {
			if (wp_style_is($style_handle, 'registered')) {
				$dependencies[] = $style_handle;
			}
		}
	}

	wp_enqueue_style(
		'child-style',
		get_stylesheet_directory_uri() . '/assets/css/main.css?v1',
		$dependencies,
		'1.0.3'
	);
}
add_action('wp_enqueue_scripts', 'child_styles');

function child_scripts()
{
	wp_enqueue_script(
		'waw-child-theme',
		get_stylesheet_directory_uri() . '/assets/js/bundle.js',
		array('jquery'),
		"1.0.0",
		[
			'strategy' => 'defer',
			'in_footer' => true
		]
	);
}
add_action('wp_enqueue_scripts', 'child_scripts', 999);

// --------------------------------------

function custom_wp_body_open()
{
	echo '<div class="page-loader">';
	echo '<div class="spinner"></div>';
	echo '<div class="txt">Loading...</div>';
	echo '</div>';
}
add_action('wp_body_open', 'custom_wp_body_open', 999);

function wpdocs_template_part_areas(array $areas)
{
	$areas[] = array(
		'area'        => 'blog',
		'area_tag'    => 'div', // Valid elements: div, header, main, section, article, aside and footer.
		'label'       => __('Blog', 'ikon-theme'),
		'description' => __('Template part area description', 'ikon-theme'),
		'icon'        => 'sidebar' // Default icons: header, footer and sidebar.
	);

	return $areas;
}
// add_filter('default_wp_template_part_areas', 'wpdocs_template_part_areas');

/**
 * Add preload tag for the hero image to improve Largest Contentful Paint (LCP).
 *
 * This function inserts a <link rel="preload"> tag into the <head> section of the page.
 * Preloading the hero image ensures it is fetched earlier, improving LCP and overall page performance.
 */
function add_preload_hero_image()
{
	// Output the preload link for the hero image
	echo '<link rel="preload" href="https://alg.edu.au/wp-content/uploads/2024/11/Homepage-Hero-1.0.min_.jpg" as="image" type="image/jpg" />' . "\n";
}

// Hook the function into wp_head to include the preload tag in the <head> section.
add_action('wp_head', 'add_preload_hero_image');


// add_action('wp_enqueue_scripts', function () {
// 	global $wp_styles;

// 	// Path to combined file
// 	$upload_dir = wp_upload_dir();
// 	$file_path = $upload_dir['basedir'] . '/waw-block-combined.css';
// 	$file_url = $upload_dir['baseurl'] . '/waw-block-combined.css';

// 	// Check if the combined file already exists
// 	if (file_exists($file_path)) {
// 		// Enqueue the existing combined file
// 		wp_enqueue_style('waw-block-combined', $file_url, [], null);
// 		return;
// 	}

// 	// Initialize an array to store style handles
// 	$waw_styles = [];

// 	// Loop through all registered styles
// 	foreach ($wp_styles->registered as $handle => $style) {
// 		// Parse the URL to extract the path
// 		$parsed_url = wp_parse_url($style->src);
// 		$path = $parsed_url['path'] ?? '';
// 		// Check if the style handle starts with 'waw-block'
// 		if (strpos($handle, 'waw-blocks') === 0 && str_ends_with($path, 'frontend.css')) {

// 			$waw_styles[] = $style->src; // Collect the style source URL
// 			wp_dequeue_style($handle); // Dequeue the style
// 		}
// 	}

// 	// If there are styles to combine
// 	if (!empty($waw_styles)) {
// 		$combined_styles = '';
// 		// var_dump($waw_styles);

// 		// Fetch and combine the styles
// 		foreach ($waw_styles as $style_src) {
// 			$style_content = wp_remote_get($style_src);
// 			if ($style_src) {
// 				if (!is_wp_error($style_content) && isset($style_content['body'])) {
// 					$combined_styles .= $style_content['body'] . "\n";
// 				}
// 			}
// 		}

// 		// Save the combined styles into a file
// 		file_put_contents($file_path, $combined_styles);

// 		// Enqueue the combined file
// 		wp_enqueue_style('waw-block-combined', $file_url, [], null);
// 	}
// }, 20);
