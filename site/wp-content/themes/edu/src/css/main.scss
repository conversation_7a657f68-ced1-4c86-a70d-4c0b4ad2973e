@import './components/reset';
@import './components/buttons';
@import './components/nav';
@import './components/forms';
@import './components/nice';
@import './components/pills';

// PATTERNS
@import './components/patterns/header-mobile';
@import './components/patterns/header-desktop';

// BLOCKS
@import './components/blocks/query';
@import './components/blocks/search-filtered';
@import './components/blocks/wp-swiper';
@import './components/blocks/icon';
@import './components/blocks/accordion';

// PARTS
@import './components/parts/hello-bar';

// responsive utilities
// due to lack of controls
// for responsive elements,
// we resort to css classes
// for adjusting alignment
// spaces and gaps
@import './components/responsive-utils';

$breakpoint-max: 768px;
$breakpoint-min: 767px;

// <containerWidth>
* {
	box-sizing: border-box;
}

.wp-block-group.is-show-seperator > :not(:last-child):after {
	background-color: #111;
}

body {
	line-height: 25px;
	overflow-x: hidden;

	/* Ensure smooth font rendering across all browsers */
	-webkit-font-smoothing: antialiased; /* For WebKit browsers like Chrome and Safari */
	-moz-osx-font-smoothing: grayscale; /* For macOS and Firefox */
	font-smooth: always; /* General smooth rendering (some browsers support) */
	text-rendering: optimizeLegibility; /* Improve readability for larger bodies of text */

	@media (min-width: 1400px) {
		.container-xxl,
		.container-xl,
		.container-lg,
		.container-md,
		.container-sm,
		.container {
			max-width: var(--wp--style--global--content-size);
		}
	}

	&.blog {
		background-color: #f5f5f5;

		.wp-block-post-title {
			flex-basis: unset;
		}
	}
}

ol {
	list-style: auto;

	li {
		margin-bottom: 20px;
	}
}

// weird margin bottom
.wp-swiper > .wp-swiper__wrapper {
	margin-bottom: 0 !important;
}

.wp-block-columns {
	max-width: var(--wp--style--global--content-size);
	margin: 0 auto;
}

.alignwide {
	max-width: var(--wp--style--global--wide-size);
}

// </containerWidth>

a {
	&:hover {
		transition: color 0.2s ease-out;
		color: unset;
	}
}

.site-header {
	// added because: subMenu.isShowing being behind other elements
	position: relative;
	z-index: 10;
	// added because: subMenu.isShowing being behind other elements
	nav {
		white-space: nowrap;

		.wp-block-navigation__container {
			gap: 30px;

			a {
				&:hover {
					color: var(--wp--preset--color--prmimary);
				}
			}
		}
	}

	@media (max-width: 1281px) {
		.wp-block-site-logo {
			padding: 20px 0;
		}
	}
}

footer {
	a:hover {
		text-decoration: underline !important;
	}

	ul {
		padding-left: 0;
	}

	li {
		display: block;
		margin-bottom: 10px;
		line-height: 26px;

		@media (max-width: 781px) {
			display: inline-block;
			width: 100%;
			float: left;
		}
	}

	.footer-icons {
		@media (min-width: 781px) and (max-width: 1023px) {
			display: none;
		}

		.wp-block-column {
			&.footer-column-social-icon {
				max-width: fit-content;

				@media (max-width: 781px) {
					margin: 0 20px 0 0;
					//flex-basis: 30% !important;
				}
			}
		}
	}

	.wp-block-spacer {
		&:first-of-type {
			@media (max-width: 781px) {
				height: 60px !important;
			}
		}
	}

	.site-by span {
		font-weight: bold;
	}

	.privacy-policy,
	.terms-conditions {
		@media (max-width: 781px) {
			//width: 50%;
			//border: 1px solid #000;
		}
	}
}

.footer-wrap-group-logos {
	&.is-layout-flex {
		align-items: flex-start;
		flex-direction: row;
	}
}

// Couses List block
.wp-block-post-template {
	.wp-block-post-featured-image {
		margin-bottom: 20px;
	}

	&-is-layout-grid {
		.wp-block-post-excerpt__excerpt {
			font-weight: 400;
			font-size: 20px;
			line-height: 148%;
			letter-spacing: 0%;
		}
		.wp-block-post-title {
			background: none;
			padding: 0;
			font-size: 18px;
			font-weight: 700;
			margin-bottom: 0;

			a {
				text-decoration: none;
				color: inherit;
			}
		}
	}
}

.wawPostCards {
	color: red;
}

.wp-swiper {
	.wp-block-cover {
		// by default this block has additional padding
		// we dont need it
		// removing it helps to align to the edge of the container
		padding: 0;
	}
}

.wp-swiper__navigation,
.wp_swiper__navigation {
	position: absolute;
	bottom: -10%;
	left: 0;
	right: 0;

	&-container {
		position: relative;
		margin: 0 auto;
		max-width: var(--wp--style--global--content-size);
	}
}

.wp-swiper.is-style-thumbnails-bottom-right .wp_swiper__navigation .wp_swiper__button-next {
	left: calc(var(--swiper-navigation-size) + 31px);
}

.wp-swiper.is-style-thumbnails-bottom-right .wp_swiper__navigation .wp_swiper__button-prev {
	left: 11px;
}

.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background) {
	border: 1px solid;
	border-color: var(--wp--preset--color--black);
}

// added z index
// to appear above nav
.wp-block-site-logo {
	z-index: 1;
}

.wp-block-post-content {
	position: relative;
	background-color: #f5f5f5;
}

// wawEventHeader
.wawEventHeader {
	.wp-block-categories {
		padding-left: 0;
		font-size: 20px;
		font-weight: 400;
		line-height: 27px;
		letter-spacing: 0em;
		text-align: left;
		margin-bottom: 15px;

		a {
			color: #fff;
		}
	}

	.wp-block-post-title {
		color: #fff;
		font-size: 56px;
		font-weight: 800;
		line-height: 76px;
		letter-spacing: 0em;
		text-align: left;

		@media (max-width: 768px) {
			font-size: 32px;
			line-height: 48px;
		}
	}

	.event-location {
		margin-right: 100px;

		@media (max-width: 768px) {
			margin-right: 20px;
		}
	}

	.wp-block-group {
		@media (max-width: 768px) {
			flex-direction: column;
			gap: 20px;
			align-items: flex-start;
		}
	}

	.event-date,
	.event-location {
		display: flex;
		align-items: center;
		color: #fff;

		font-size: 16px;
		font-weight: 500;
		line-height: 26px;
		letter-spacing: 0px;
		text-align: left;

		span {
			padding-top: 4px;
		}

		svg {
			margin-right: 10px;
		}
	}
}

.withWhitePanel {
	position: relative;

	h2 {
		font-size: 20px;
		font-weight: 500;
		line-height: 24px;
		letter-spacing: 0em;
		text-align: left;
		background-color: #fff;
		padding: 20px 35px 20px 20px;
		margin: 0;
		margin-bottom: 0; // reset to 0, was adding an extra space for courses cards under title

		@media (max-width: 781px) {
			font-size: 16px !important;
		}

		@media (min-width: 782px) and (max-width: 1440px) {
			font-size: 18px !important;
		}
	}

	a {
		text-decoration: none;
		color: inherit;
	}

	svg {
		position: absolute;
		right: 20px;
		top: 50%;
		transform: translateY(-50%);
		max-width: 10px;
		transition: all 0.4s ease-out;
	}

	&.withSideBorder {
		border-left: 8px solid #d45e2b !important;

		&:hover {
			a {
				color: #d45e2b;
			}
			svg {
				margin-right: 10px;
			}
		}
	}
}

// affect cta pattern
@media (max-width: 768px) {
	.wp-block-group {
		flex-direction: column;
	}
}

.wp-block-cover {
	.wp-block-cover__inner-container {
		padding-right: calc(1.5rem * 0.5);
		padding-left: calc(1.5rem * 0.5);
	}
}

.section-home-explore {
	padding: 40px 20px;
}

.thumbnail-link {
	color: blue !important;
}

/*************** Danilo CSS *********************/

p {
	margin-bottom: 21px;
}

.aligncenter {
	margin-left: auto;
	margin-right: auto;
}
.wawSubMenu hr {
	margin-right: 40%;
	margin-top: 10px !important;
	margin-bottom: 20px !important;
	display: block;
}

.wawSubMenu {
	.wawSearchCourses input[type='search'] {
		width: 100%;
		height: 40px;
		border-radius: 40px;
		box-shadow: unset;
		min-width: 320px;
		padding: 10px 30px;
		border: none;
		border: 1px solid #7c7c7c;

		&:hover {
			border: 1px solid rgba(0, 0, 0, 0.2);
			outline: none;
			cursor: pointer;
		}
	}

	.wawSearchCourses button {
		position: absolute;
		border: none;
		background: none;
		top: 50%;
		right: 0;
		cursor: pointer;
		transform: translateY(-50%);
		font-size: 0;

		svg {
			path:first-of-type {
				fill: #fff;
			}

			path:last-of-type {
				fill: #135758;
			}
		}
	}
}

/*** Outline btn ***/
body .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap: wrap;
	//height: 55px;
	border: 1px solid;

	color: var(--wp--preset--color--primary);
	white-space: normal;

	&:hover {
		background-color: var(--wp--preset--color--primary) !important;
		border: 1px solid var(--wp--preset--color--primary) !important;
	}
	&:active {
		background-color: var(--wp--preset--color--primary) !important;
		border: 1px solid var(--wp--preset--color--primary) !important;
	}
	&:disabled {
		background-color: transparent !important;
		color: #66829c !important;
		border: 1px solid #66829c !important;
	}
}

.wp-block-button.is-style-fill .wp-block-button__link.wp-element-button {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap: wrap;
	//height: 55px;
	border: 1px solid;
	border-color: var(--wp--preset--color--primary);

	&:hover {
		border-color: var(--wp--preset--color--primary) !important;
		background-color: #fff !important;
		color: var(--wp--preset--color--primary) !important;
	}
}

.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background) {
	border: 1px solid;
	border-color: var(--wp--preset--color--primary);
}

/*** has-white-background-color btn ***/

body .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color {
	display: flex;
	align-content: center;
	justify-content: center;
	flex-wrap: wrap;
	//height: 55px;
	border: 1px solid;
	border-color: var(--wp--preset--color--white);
	color: var(--wp--preset--color--primary);

	&:hover {
		background-color: var(--wp--preset--color--primary) !important;
		border: 1px solid var(--wp--preset--color--primary) !important;
		color: #fff !important;
	}
	&:active {
		background-color: var(--wp--preset--color--primary) !important;
		border: 1px solid var(--wp--preset--color--primary) !important;
	}
	&:disabled {
		background-color: #e6e6e6 !important;
		color: #66829c !important;
		border: 1px solid #e6e6e6 !important;
	}
}

/*********  HEADER BUTTON  *********/

.wawNavigation nav a.wp-element-button {
	&:hover,
	&:focus,
	&:active {
		color: var(--wp--preset--color--white) !important;
	}
}

.wp-block-query .wp-block-post-featured-image {
	height: auto;
	flex-grow: 1;

	img {
		height: 100%;
	}
}

body .wp-block-media-text {
	&.is-image-fill .wp-block-media-text__media img {
		width: 100%;
		height: auto;
		position: relative;
	}

	&.has-media-on-the-right.is-stacked-on-mobile.is-image-fill {
		h2 {
			margin-top: 0;
		}
	}
}

body .da-accordion__item-heading .svg svg,
body .da-accordion__item > a.active svg {
	stroke: #00549f;
}

.waw-tabs__background {
	background-color: var(--wp--preset--color--primary) !important;
	display: none;
}

.waw-tabs__header {
	max-width: 80%;
	margin-right: auto;
	margin-left: auto;
	background-color: white;

	h4 {
		transition: all 0.4s ease-out;
	}

	.is-active {
		background-color: var(--wp--preset--color--primary) !important;
		border-radius: 10px;
	}
}

.nav-header {
	background-color: #fff;
	padding: 15px 0;
	box-shadow: 0px 0px 5px #00000078;
}

.second-li-list-style-none {
	margin-bottom: 15px;

	li {
		margin-bottom: 0;
		&:nth-child(2) {
			list-style: none;
		}
	}
}

.hentry.event_category-event-category {
	.wawEventMeta {
		&:hover svg {
			fill: #d45e2b;
		}

		.wawEventMeta__link {
			svg {
				fill: #d45e2b;
			}
		}
	}
}

.wawEventMeta__link {
	svg {
		fill: #d45e2b;
	}
	&:hover {
		svg {
			fill: #d45e2b;
		}
	}
}

.da-icontextblock.is-style-boxed-content-centered {
	margin-bottom: 20px;
	padding: 0;
	background: none;

	.da-icontextblock__title {
		margin-top: 20px;
	}

	.da-icontextblock__image {
		border-radius: unset;
		border: none;
		overflow: hidden;

		img {
			width: 100%;
		}
	}
}

// Added this for submenus
// max-width; needed for screens starting from 1280 and down (mobile), like laptops
// now adding flex wrap too, other wise submenu items are not stacked

@media (max-width: $breakpoint-max) {
	.wp-block-columns {
		flex-wrap: wrap !important;
	}
}
.wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
	@media (max-width: $breakpoint-max) {
		flex-basis: 100% !important;
	}
	@media (min-width: $breakpoint-min) {
		flex-basis: 0;
		flex-grow: 1;
		margin: 0 10px;
	}
}

.border-bottom-radius-12 {
	border-bottom-left-radius: 12px;
	border-bottom-right-radius: 12px;
}

.container-padding-single-post {
	.wawContainer__content {
		.container {
			padding: 0 100px;

			.wp-block-image img {
				border-radius: 12px;
			}
		}
	}
}

.container-wrapper-form {
	.wawContainer__content {
		.container {
			max-width: 800px;
		}
	}
}

.img-top-radius-12px {
	img {
		border-top-left-radius: 12px;
		border-top-right-radius: 12px;
	}
}

.img-radius-12px {
	img {
		border-radius: 12px;
	}
}

.wp-block-cover .has-background-dim:not([class*='-background-color']),
.wp-block-cover-image .has-background-dim:not([class*='-background-color']),
.wp-block-cover-image.has-background-dim:not([class*='-background-color']),
.wp-block-cover.has-background-dim:not([class*='-background-color']) {
	background-color: unset;
}

.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn)) > button:not([id*='mceu_']):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type='submit'], [type='button'], [type='reset']).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type='submit'], [type='button'], [type='reset']):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input[type='submit'].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) {
	width: 320px;
	height: 48px;
	margin-left: 0;

	background: none;

	border-radius: 6px;
	border-radius: 4px;
	border-width: 1.5px;
	border-color: #135758;

	color: #135758;
	font-size: 16px;
	font-weight: 400;
	line-height: 19px;
	letter-spacing: 0.03em;
	text-align: center;

	&:hover {
		background-color: #135758;
		color: #fff;
	}
}

form {
	&.wp-container-content-1 {
		@media (max-width: 767px) {
			flex-basis: unset;
		}
	}
}

// BUTTONS
// on the mobile buttons shouldnt be limited in width
.wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
	width: initial;

	@media (min-width: 768px) {
		width: calc(50% - var(--wp--style--block-gap, 0.5em) * 0.5);
	}
}

/**************  OVERWRITING ***********/

.container-lg,
.container-md,
.container-sm,
.container {
	@media (min-width: 100px) {
		max-width: 100%;
	}

	@media (min-width: 992px) {
		max-width: 1200px;
	}
}

.ready-to-get-started-section {
	.is-content-justification-right,
	.is-content-justification-left {
		@media (max-width: 781px) {
			justify-content: center;
			padding-bottom: 15px;
		}
	}

	.wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
		@media (max-width: 781px) {
			width: 90% !important;
		}
	}
}

.site-footer {
	a {
		text-decoration: none;
	}
	h5 {
		@media (max-width: 781px) {
			margin-top: 0;
		}
	}
}

.container-wrapper-form {
	.wawContainer.is-style-with-border .wawContainer__content {
		@media (max-width: 781px) {
			padding: 49px 10px;
		}
	}

	.col {
		@media (max-width: 781px) {
			padding: 0;
		}
	}
}

// Breaks courses block, adds a bullet point
// Blog Styles
// adding .single-post
.entry-content {
	ul:not(.wp-block-post-template) {
		ol,
		li {
			list-style: initial;
		}
		@media (max-width: 1200px) {
			padding-top: 0 !important;
		}
	}
}

.container-top-form-text {
	.container {
		@media (min-width: 1024px) and (max-width: 1200px) {
			max-width: 82%;
		}

		@media (min-width: 1201px) and (max-width: 1400px) {
			max-width: 72%;
		}
		@media (min-width: 1401px) {
			max-width: 62%;
		}
	}
}

h2 {
	@media (max-width: 782px) {
		font-size: 32px !important;
	}
}

.wawContainer.is-style-with-border.wrapper-contact-form {
	.wawContainer__content {
		@media (max-width: 1024px) {
			padding: 40px 15px;
		}
	}
}

.h2-contact-form {
	@media (max-width: 782px) {
		margin-bottom: 0;
	}
}

.info-sidebar {
	.is-layout-flex {
		@media (max-width: 781px) {
			display: inline-block;
			text-align: left;
		}

		.wawSvgIcon,
		p {
			@media (max-width: 781px) {
				float: left;
			}
		}
	}

	h5 {
		@media (max-width: 781px) {
			margin-bottom: 30px !important;
		}
	}

	&.wawContainer.is-style-with-border {
		.wawContainer__content {
			@media (max-width: 1024px) {
				padding: 40px 15px;
			}
		}
	}
}

.is-style-overlayed-text-right {
	.wp-swiper__slide-content {
		@media (max-width: 781px) {
			display: block;
		}

		.wp-block-columns {
			.wp-block-column,
			.wp-block-column:last-child {
				@media (max-width: 781px) {
					width: 100%;
					max-width: 100% !important;
					display: block;
					position: relative !important;
					padding: 0 !important;
					margin-right: 0 !important;
				}
			}

			.wp-block-column:last-child {
				@media (max-width: 781px) {
					padding: 20px !important;
				}
			}
		}
	}
}

.container-bg-locations {
	.wp-block-query.is-style-two-columns ul {
		@media (max-width: 1200px) {
			grid-template-columns: repeat(1, 1fr);
		}
	}

	.is-content-justification-right,
	.is-layout-flex.wp-block-buttons-is-layout-flex {
		@media (max-width: 781px) {
			justify-content: center;
		}
	}
}

.hide-mobile {
	@media (max-width: 781px) {
		display: none;
	}
	@media (min-width: 782px) {
		display: block;
	}
}

.show-mobile {
	@media (max-width: 781px) {
		display: block;
	}

	@media (min-width: 782px) {
		display: none;
	}
}

.course-list {
	&.wp-block-query.is-style-two-columns ul {
		@media (max-width: 781px) {
			grid-template-columns: repeat(1, 1fr);
		}
	}

	h2 {
		&.wp-block-post-title {
			@media (max-width: 781px) {
				font-size: 16px !important;
			}

			@media (min-width: 782px) and (max-width: 1440px) {
				font-size: 18px !important;
			}
		}
	}
}

.section-tiles-home-page {
	.wp-block-buttons {
		@media (max-width: 781px) {
			margin-bottom: 0 !important;
		}
	}

	.is-style-80-gap {
		@media (max-width: 781px) {
			gap: 0 !important;
			padding-bottom: 60px !important;
		}
	}
}

.column-section-map-content {
	.link-with-arrow {
		flex-direction: row;
	}
}

iframe {
	width: 100%;
}

body .wp-swiper.is-style-thumbnails-bottom-right {
	.wp-swiper__thumbs {
		@media (max-width: 781px) {
			width: 80%;
		}
	}
}

/*****************  TABLE  ************************/

.wawCoursesTable {
	margin-bottom: 0 !important;
	table {
		border: 1px solid #ccc;
		border-collapse: collapse;
		margin: 0;
		padding: 0;
		width: 100%;
		table-layout: fixed;
	}

	table caption {
		font-size: 1.5em;
		margin: 0.5em 0 0.75em;
	}

	table tr {
		border: 1px solid #ddd;
		padding: 0.35em;
	}

	table th,
	table td {
		padding: 0.625em;
		text-align: center;
	}

	table th {
		font-size: 0.85em;
		letter-spacing: 0.1em;
		text-transform: uppercase;
	}

	@media screen and (max-width: 781px) {
		table {
			border: 0;
		}

		table caption {
			font-size: 1.3em;
		}

		table thead {
			border: none;
			clip: rect(0 0 0 0);
			height: 1px;
			margin: -1px;
			overflow: hidden;
			padding: 0;
			position: absolute;
			width: 1px;
			color: #000 !important;
		}

		table tr {
			border-bottom: 3px solid #ddd;
			display: block;
			margin-bottom: 0.625em;
		}

		table td {
			border-bottom: 1px solid #ddd;
			display: block;
			font-size: 0.8em;
			text-align: right !important;
		}

		table td.table-title {
			text-align: left !important;
		}

		table td::before {
			/*
		  * aria-label has no advantage, it won't be read inside a table
		  content: attr(aria-label);
		  */
			content: attr(data-label);
			float: left;
			font-weight: bold;
			text-transform: uppercase;
		}

		table td:last-child {
			border-bottom: 0;
		}
	}
}

/******************** NEW TABLES  *****************/
.banner-text-btn .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:hover {
	border: 1px solid #fff !important;
}

.wp-block-heading.has-text-align-center {
	@media screen and (max-width: 781px) {
	}
}

.courses-list-block.wp-block-query.is-style-two-columns ul {
	display: grid;
	grid-auto-rows: minmax(min-content, max-content);
	grid-template-columns: repeat(3, 1fr);

	@media (min-width: 782px) and (max-width: 1023px) {
		grid-template-columns: repeat(2, 1fr);
	}

	@media (max-width: 781px) {
		grid-template-columns: repeat(1, 1fr);
	}

	.wp-block-post-title {
		@media (max-width: 1400px) {
			font-size: 16px !important;
		}
	}
}

.btn-link-text-plus-arrow {
	display: flex !important;
	flex-direction: row !important;
}

.section-image-right {
	.wp-block-heading {
		@media (max-width: 781px) {
			font-size: 32px !important;
			text-align: left;
		}
	}

	p {
		@media (max-width: 781px) {
			max-width: 100% !important;
		}
	}

	ul {
		@media (max-width: 781px) {
			max-width: 100% !important;
		}

		li {
			@media (max-width: 781px) {
				text-align: left;
			}
		}
	}

	.wp-block-spacer {
		@media (max-width: 781px) {
			height: 50px !important;
		}
	}

	.wp-block-spacer {
		@media (max-width: 781px) {
			height: 90px !important;
		}
	}
}

.section-image-left {
	.wp-block-heading {
		@media (max-width: 781px) {
			font-size: 32px !important;
			text-align: left;
			max-width: none;
		}
	}

	p {
		@media (max-width: 781px) {
			max-width: 100% !important;
		}
	}
	ul {
		@media (max-width: 781px) {
			max-width: 100% !important;
		}

		li {
			@media (max-width: 781px) {
				text-align: left;
			}
		}
	}

	.wp-block-spacer {
		@media (max-width: 781px) {
			height: 90px !important;
		}
	}
}

.accordion-resources {
	h3 {
		@media (max-width: 781px) {
			font-size: 36px !important;
		}
	}
}

.waw-tabs__item {
	margin-top: 20px;
	border-bottom: unset !important;

	@media (min-width: 1023px) {
		margin-top: 40px;
	}
}

.media-columns-flex-overwrite {
	.media-column-left {
		@media (max-width: 1023px) {
			flex-basis: 100% !important;
		}

		@media (min-width: 1024px) and (max-width: 1200px) {
			flex-basis: 60% !important;
		}
	}

	.media-column-right {
		@media (max-width: 1023px) {
			flex-basis: 100% !important;
		}

		@media (min-width: 1024px) and (max-width: 1200px) {
			flex-basis: 40% !important;
		}
	}
}

/*
*  TABS ACCORDION
*/

.wawContainer.container-block-tab {
	.wawContainer__content {
		@media (max-width: 1023px) {
			padding: 15px !important;
		}
	}
	.waw-tabs__item-content {
		padding: 0 20px 20px 20px;

		@media (max-width: 1023px) {
			padding: 0 !important;
		}
	}

	.da-accordion__item-content {
		padding: 0 20px 20px 20px;

		@media (max-width: 1023px) {
			padding: 0 !important;
		}
	}
}

/******************  BUTTONS  *******************/

/****************   NEW UPDATES  *****************/

.dm-list {
	li {
		margin-bottom: 0 !important;
	}
}

/******************  CONTAINER TWO TILES ***************************/
.container-post-two-tiles .wp-block-group {
	border-bottom-left-radius: 12px;
	border-bottom-right-radius: 12px;
}
.container-post-two-tiles figure {
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
}
@media (max-width: $breakpoint-max) {
	.container-post-two-tiles .wp-block-query.is-style-two-columns ul {
		grid-template-columns: repeat(1, 1fr);
	}
}
@media (max-width: 1281px) {
	.container-post-two-tiles .is-content-justification-right,
	.container-post-two-tiles .is-layout-flex {
		justify-content: center;
	}
}

/****************************  banner-image-two-buttons **********************/
@media (max-width: 781px) {
	.waw-banner-image-two-buttons .is-content-justification-right,
	.waw-banner-image-two-buttons .is-layout-flex {
		justify-content: center;
	}
}

/* /**************************  PATTERN waw-icons-text-pattern ******************/
@media (max-width: 781px) {
	.waw-icons-text-pattern p {
		max-width: 80%;
		margin-left: auto;
		margin-right: auto;
	}
}

@media (max-width: 781px) {
	.hide-mobile {
		display: none;
	}
	.show-mobile {
		display: block;
	}
}
@media (min-width: 782px) {
	.hide-mobile {
		display: block;
	}
	.show-mobile {
		display: none;
	}
}

/******** PAGE EVENTS  ************/
@media (max-width: 1023px) {
	.wp-block-query.is-style-two-columns ul {
		display: grid;
		grid-template-columns: repeat(1, 1fr);
		grid-auto-rows: minmax(min-content, max-content);
		// gap: 0 0; disable this, as it removes gap from courses grid on mobile
	}
}

.wp-block-button__link {
	min-width: 200px;
}

/*****  PAGE COURSES TWO COLUMNS QUERIES ****/

.key-information {
	.col {
		padding-left: 0;
		padding-right: 0;
	}
}

@media (max-width: 1023px) {
	.two-columns-size-overwrite .wp-block-columns.is-layout-flex.wp-container-core-columns-layout-10.wp-block-columns-is-layout-flex {
		display: grid;
		grid-template-columns: repeat(1, 1fr);
	}
}
@media (max-width: 781px) {
	.two-columns-size-overwrite .wp-block-query.is-style-two-columns ul {
		display: grid;
		grid-template-columns: repeat(1, 1fr);
		grid-auto-rows: minmax(min-content, max-content);
		gap: 0 0;
	}
}
@media (min-width: 782px) and (max-width: 1023px) {
	.two-columns-size-overwrite .wp-block-query.is-style-two-columns ul {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-auto-rows: minmax(min-content, max-content);
		gap: 10px 20px;
	}
}

// <tables>
// make tables responsive again
@media (max-width: 1280px) {
	.wp-block-table {
		table {
			display: block;
			td {
				white-space: nowrap;
				padding: 20px 5px !important;
			}
		}
	}
}
// </tables>

/****************  IMPORTANT DATES  ****************/
@media (max-width: 1023px) {
	.wawContainer.container-tabs-table .wawContainer__content {
		padding: 15px !important;
	}
}
@media (max-width: 1023px) {
	.wawContainer.container-tabs-table .waw-tabs__item-content {
		padding: 0;
	}
}
@media (max-width: 782px) {
	.wawContainer.container-tabs-table .wp-block-table.is-style-stripes tbody tr > td {
		border-right: unset;
		padding: 5px 20px;
	}
}

/**************************  POSTS CONTENT  **************/
.content-post .container {
	max-width: 75%;
}
@media (max-width: 781px) {
	.content-post .container {
		max-width: 90%;
	}
}

.hide-block-important {
	display: none;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-post-title {
	font-size: 28px;
}
.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .taxonomy-category {
	margin-bottom: 10px;
	font-size: 15px;
}
.query-loop-content-feature ul.wp-block-post-template li.wp-block-post figure.wp-block-post-featured-image img {
	border-radius: 6px;
	margin-bottom: 30px;
}
.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-post-excerpt {
	margin-bottom: 18px;
}
.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-read-more {
	display: flex;
	flex-direction: row;
	color: #d45e2b;
}
.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-read-more:after {
	position: relative;
	content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
	padding-left: 8px;
	top: 2px;
}

.wp-block-archives-list.wp-block-archives li {
	list-style: none;
}

.query-loop-archive ul.wp-block-post-template li.wp-block-post {
	margin-bottom: 30px;
}
.query-loop-archive ul.wp-block-post-template li.wp-block-post .wp-block-post-title {
	font-size: 15px;
}

/*************************  POST TILES 3 COLUMNS  ************************/
.query-loop--flat ul {
	gap: 15px;
}
@media (max-width: 781px) {
	.query-loop--flat ul.wp-container-core-post-template-layout-1 {
		grid-template-columns: 1fr;
		gap: 20px 20px;
	}
}
@media (min-width: 782px) and (max-width: 1023px) {
	.query-loop--flat ul.wp-container-core-post-template-layout-1 {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-auto-rows: minmax(min-content, max-content);
		gap: 10px 20px;
	}
}
.query-loop--flat .wp-block-post .wp-block-post-featured-image img {
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
}
.query-loop--flat .wp-block-post .wp-block-group.has-background {
	border-bottom-left-radius: 8px;
	border-bottom-right-radius: 8px;
}
.query-loop--flat .wp-block-post .wawMeta {
	font-weight: bold;
}
.query-loop--flat .wp-block-post-excerpt__more-text {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	margin-top: 30px;
}
.query-loop--flat .wp-block-post-excerpt__more-text:after {
	position: relative;
	content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
	padding-left: 8px;
	top: 2px;
}
.query-loop--flat .wp-block-post-excerpt__more-text a {
	color: #d45e2b;
}

/*******************  LINK WITH ARROW  ****************/
@media (max-width: 781px) {
	.link-with-arrow.is-layout-flex {
		display: flex;
		flex-direction: row;
	}
}

/***********************  WHAT WE OFFER BLOCK  **************/
.what-we-offer-block h4 {
	margin-bottom: 5px;
}

/*********************  EVENTS PAGE  *********************/
@media (max-width: 781px) {
	.container-icons-events-page {
		width: 100%;
		padding: 10px 0;
	}
}
@media (max-width: 781px) {
	.container-icons-events-page .wp-block-group {
		width: 100%;
		padding-left: 0 !important;
		margin-bottom: 10px;
		flex-direction: row !important;
	}
}

/******************* TOP NAV BAR LOGIN SEARCH - NAVEGATION   *************/
.hello-bar__wrapper a:hover,
.wawNavigation a:hover {
	color: var(--wp--preset--color--primary) !important;
}

/********************* FLEX LEFT  *****************/
@media (max-width: 1023px) {
	.flex-left-on-mobile {
		flex-direction: row !important;
		padding: 0 15px !important;
	}
}

.element-90percent {
	max-width: 90%;
	margin-left: auto;
	margin-right: auto;
}

/*********************  CATEGORIES TILES  ********************/
.wawCategories .wawCategories__wrapper {
	grid-template-columns: repeat(4, 1fr);
	gap: 15px;
}
@media (min-width: 1024px) and (max-width: 1200px) {
	.wawCategories .wawCategories__wrapper {
		grid-template-columns: repeat(3, 1fr);
	}
}
@media (min-width: 872px) and (max-width: 1023px) {
	.wawCategories .wawCategories__wrapper {
		grid-template-columns: repeat(2, 1fr);
	}
}
@media (max-width: 871px) {
	.wawCategories .wawCategories__wrapper {
		grid-template-columns: repeat(1, 1fr);
	}
}
.wawCategories .wawCategories__wrapper .wawCategories__item {
	margin-bottom: 20px;
}

.wawCategories {
	.wawCategories__item-title {
		svg {
			transition: all 0.4s ease-out;
		}

		&:hover {
			a {
				color: #d45e2b;
			}
			svg {
				margin-right: 10px;
			}
		}
	}
}

/********************* HEADER  *************/

.waw-header {
	@media (max-width: 871px) {
		margin-top: 0;
		margin-bottom: 0;
	}

	@media (min-width: 872px) and (max-width: 1200px) {
		margin-top: 10px;
		margin-bottom: 10px;
	}

	@media (max-width: 1023px) {
		flex-direction: row !important;
	}

	.wp-element-button.wp-block-button__link {
		padding: 0.667em 1.333em !important;
	}
}

.dm-header-button {
	.wp-block-button__link {
		min-width: 135px;
	}
}

nav {
	transition: all 0.4s ease-out;

	.wawNavigation nav ul {
		gap: 15px !important;

		li {
			a {
				font-weight: 500;
			}
		}

		a {
			text-decoration: none;
		}
	}

	.hello-bar__wrapper {
		padding-right: 10px;
	}

	.container-tabs-table {
		.wp-block-table.is-style-stripes tbody tr > td {
			@media (max-width: 500px) {
				font-size: 10px;
				line-height: 14px;
			}
		}

		.wawContainer__content {
			@media (max-width: 500px) {
				padding: 0 !important;
			}

			.container {
				padding: 0 !important;
			}
		}
	}
}

/*******************************  IMPORTANT DATES  **************************/
.container-tabs-table {
	.wp-block-table.is-style-stripes tbody tr > td {
		@media (max-width: 640px) {
			font-size: 10px;
			line-height: 14px;
		}
	}

	.wawContainer__content {
		@media (max-width: 640px) {
			padding: 0 !important;
		}

		.container {
			padding: 0 !important;
		}
	}
}

.block-partner {
	ul {
		margin-top: 25px;
	}
}

.slider-logos {
	.swiper-wrapper {
		justify-content: center !important;
	}

	.swiper-slide {
		width: auto !important;
	}

	.slider-row {
		img {
			margin: 40px;
		}
	}
}

.slider-logos-2 {
	.swiper-wrapper {
		justify-content: center !important;
	}

	.swiper-slide {
		width: auto !important;
	}

	.slider-row {
		img {
			margin: 40px 80px;
		}
	}
}

.slider-logos-3 {
	.swiper-wrapper {
		justify-content: center !important;
	}

	.swiper-slide {
		width: auto !important;
	}

	.slider-row {
		img {
			margin: 40px;
		}
	}
}

.column-wrap {
	@media (max-width: $breakpoint-max) {
		flex-direction: column;
	}
}

/**************** HOME PAGE CATEGORY  ****************/

.column-tiles-categories {
	display: grid !important;
	grid-template-columns: repeat(4, 1fr);
	gap: 15px;
	margin-bottom: 20px;

	@media (max-width: 871px) {
		grid-template-columns: repeat(1, 1fr);
	}

	@media (min-width: 872px) and (max-width: 1023px) {
		grid-template-columns: repeat(2, 1fr);
	}

	@media (min-width: 1024px) and (max-width: 1200px) {
		grid-template-columns: repeat(3, 1fr);
	}

	.column-tile {
		margin-left: 0 !important;
		margin-right: 0 !important;

		figure {
			height: 246px;

			img {
				height: 100%;
				-o-object-fit: cover;
				object-fit: cover;
				width: 100%;
			}
		}

		.column-tile-text {
			align-items: center;
			background-color: #fff;
			display: flex;
			height: 74px;
			justify-content: space-between;
			position: relative;
			flex-direction: row;

			&:hover {
				a {
					color: #d45e2b;

					&:after {
						right: 30px;
					}
				}
			}

			p {
				margin-bottom: 0;
			}

			a {
				transition: all 0.4s ease-out;

				&:after {
					transition: all 0.4s ease-out;
					height: 20px;
					width: 12px;
					content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
					position: absolute;
					right: 20px;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}
}

/*
.category-group-tile{
	width: 25%;
    float: left;
    margin-left: -15px;
    padding-left: 15px;
    margin-right: 19px;
}
*/

.categories-container-tiles {
	a {
		text-decoration: none;
	}
	.col {
		@media (max-width: 871px) {
			grid-template-columns: repeat(1, 1fr) !important;
		}

		@media (min-width: 872px) and (max-width: 1023px) {
			grid-template-columns: repeat(2, 1fr) !important;
		}

		@media (min-width: 1024px) and (max-width: 1200px) {
			grid-template-columns: repeat(3, 1fr) !important;
		}

		figure {
			height: 246px;

			a,
			img {
				height: 100%;
			}
			img {
				-o-object-fit: cover;
				object-fit: cover;
				width: 100%;
			}
		}

		.column-tile-text {
			align-items: center;
			background-color: #fff;
			display: flex;
			height: 66px;
			justify-content: space-between;
			position: relative;
			flex-direction: row;

			&:hover {
				a {
					color: #d45e2b;

					&:after {
						right: 30px;
					}
				}
			}

			p {
				margin-bottom: 0;
			}

			a {
				transition: all 0.4s ease-out;

				&:after {
					transition: all 0.4s ease-out;
					height: 20px;
					width: 12px;
					content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
					position: absolute;
					right: 20px;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}
}

/************************  TERMS AND CONDITIONS PAGE  ******************/

.terms-conditions-block {
	ul {
		li {
			margin-bottom: 5px;
			list-style: disc;
		}
	}

	ol {
		li {
			margin-bottom: 5px;
		}
	}
}

/************************  HEADER   *******************/

.magnifying-glass {
	cursor: pointer;
}

/********************************  SEARCH  *********************************/

.search-results {
	h2 {
		text-align: left !important;

		@media (max-width: 871px) {
			font-size: 16px !important;
		}
	}

	.wp-block-query-pagination {
		margin-top: 40px;
	}
}

/************************** HIDE HEADER SEARCH MENU ON SEARCH PAGE *******************/

.search-results .hide-on-search {
	display: none !important;
}

/**************************  NEW CAMPUS TILES SECTION ***************************/

.columns-tiles-wrap {
	@media (max-width: 1080px) {
		display: grid !important;
	}

	.column-tile {
		border-radius: 12px;

		.wp-block-button__link {
			@media (min-width: 1080px) and (max-width: 1400px) {
				font-size: 14px;
				padding-left: 15px !important;
				padding-right: 15px !important;
			}
		}

		figure {
			img {
				border-top-left-radius: 12px;
				border-top-right-radius: 12px;
				min-width: 100%;
			}
		}

		.columns-btn {
			.wp-block-buttons {
				@media (max-width: 782px) {
					justify-content: center;
				}

				.wp-block-button {
					@media (max-width: 782px) {
						width: 80%;
					}
				}
			}
		}
	}
}

// testimonials overflow
.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
	max-height: 350px;
	overflow: hidden;
	overflow-y: scroll;
	border-radius: 12px;
	box-shadow: 0px 0px 15px 0px #00000040;

	/* width */
	&::-webkit-scrollbar {
		width: 3px;
		margin-top: 5px;
		margin-bottom: 3px;
	}

	/* Track */
	&::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	/* Handle */
	&::-webkit-scrollbar-thumb {
		background: #888;
	}

	/* Handle on hover */
	&::-webkit-scrollbar-thumb:hover {
		background: #555;
	}
}

// swiper thumbs
.wp-swiper__thumbs .wp-swiper__thumb {
	height: 175px;
	border-radius: 12px;

	img {
		border-radius: inherit;
	}
}

//  preloader
/* Pre-loader CSS */
.page-loader {
	position: fixed;
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 100%;
	height: 100vh;
	background: #fff;
	z-index: 1000;

	.txt {
		color: #666;
		text-align: center;
		text-transform: uppercase;
		letter-spacing: 0.3rem;
		font-weight: bold;
		line-height: 1.5;
	}
}

/* Spinner animation */
.spinner {
	width: 80px;
	height: 80px;
	margin: 0 auto;
	background-color: #ccc;
	border-radius: 100%;
	-webkit-animation: sk-scaleout 1s infinite ease-in-out;
	animation: sk-scaleout 1s infinite ease-in-out;
}

@-webkit-keyframes sk-scaleout {
	0% {
		-webkit-transform: scale(0);
		transform-origin: center center;
	}
	100% {
		-webkit-transform: scale(1);
		opacity: 0;
	}
}

@keyframes sk-scaleout {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		transform-origin: center center;
	}
	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 0;
	}
}
//  preloader

// Override WordPress Global Styles unwanted spacing
// This removes the 24px margin that WordPress adds between blocks

// Override WordPress Global Styles site blocks spacing
:where(.wp-site-blocks) > * {
	margin-block-start: 0 !important;
	margin-block-end: 0 !important;
}

// swiper next prev buttons
.wp-swiper.is-style-overlayed-text-right .wp_swiper__button-prev img,
.wp-swiper.is-style-overlayed-text-right .wp_swiper__button-next img {
	width: 45px;
	height: 45px;
}

body .wp-block-button.is-style-outline.hover-variant-1 .wp-block-button__link.wp-element-button {
	&:hover {
		border: 1px solid #fff !important;
		background-color: #fff !important;
		color: #000 !important;
	}
}

.seperator-variant-1.wp-block-group.is-show-seperator > :not(:last-child):after {
	background-color: #fff !important;
}

.da-accordion__item:first-of-type {
	border-top: 1px solid #ddd;
}

// trading view container needs a set height
.contaienr-height-400 {
	.wawContainer__content .col {
		height: 600px;
	}
}
