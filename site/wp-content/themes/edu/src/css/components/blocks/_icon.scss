.da-icontextblock.is-style-centered-showcase-box-style {
	margin: auto;
	box-shadow: 0 0 15px 5px rgba(0, 0, 0, 0.07);
	background: #000;
	border: none;
	position: relative;
	overflow: hidden;
	max-height: 463px;
	max-width: 460px;
	text-align: center;
	cursor: pointer;
	padding: 0;
	border-radius: 3px;

	.da-icontextblock__image {
		position: relative;
		display: block;
		min-height: 100%;
		border-radius: 0;
		border: none;
		transform: none;

		.da-icontextblock__image-link {
			display: block;
			width: 100%;
			height: 100%;
		}

		&-sizer {
			height: 100%;
		}

		.da-icontextblock__image-element {
			opacity: 0.8;
			transition: opacity 0.35s, transform 0.35s;
			will-change: transform;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.da-icontextblock__content {
		padding: 1em;
		color: #fff;
		text-transform: uppercase;
		font-size: 1.25rem;
		backface-visibility: hidden;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transition: justify-content 0.35s ease;

		&::before {
			position: absolute;
			content: '';
			opacity: 0;
			top: 50px;
			right: 30px;
			bottom: 50px;
			left: 30px;
			border-top: 2px solid rgba(255, 255, 255, 0.3);
			border-bottom: 2px solid rgba(255, 255, 255, 0.3);
			transform: scale(0, 1);
			transform-origin: 0 0;
			transition: opacity 0.35s, transform 0.35s;
		}

		&::after {
			position: absolute;
			content: '';
			opacity: 0;
			top: 30px;
			right: 50px;
			bottom: 30px;
			left: 50px;
			border-right: 1px solid #fff;
			border-left: 1px solid #fff;
			transform: scale(1, 0);
			transform-origin: 100% 0;
			transition: opacity 0.35s, transform 0.35s;
		}
	}

	.da-icontextblock__header {
		transition: transform 0.35s;
		transform: translate3d(0, -30px, 0);
	}

	.da-icontextblock__title {
		color: #fff;
		margin: 0;
		font-size: 24px;
		font-weight: 600;
		text-transform: capitalize;

		.da-icontextblock__link {
			color: inherit;
			text-decoration: none;
			display: inline-block;
			transition: color 0.3s ease;

			&:hover {
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	.da-icontextblock__body {
		color: rgba(255, 255, 255, 0.9);
		margin: 0;
		padding: 0.5em 2.7em;
		text-transform: none;
		opacity: 0;
		transform: translate3d(0, -10px, 0);
		font-size: 0.92rem;
		line-height: 16px;
		font-family: Poppins;
		transition: opacity 0.35s, transform 0.35s;
	}

	.da-icontextblock__button {
		margin-top: 20px;
		text-align: center;
		z-index: 1000;
		opacity: 0;
		transform: translateY(10px);
		transition: all 0.5s ease-in-out;

		.da-icontextblock__button-element {
			display: inline-block;
			padding: 8px 20px;
			color: #fff;
			text-decoration: none;
			border-radius: 3px;
			font-size: 16px;
			text-transform: capitalize;
			letter-spacing: 0.5px;
			transition: all 0.3s ease;

			&:hover {
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	// Hover effects
	&:hover {
		.da-icontextblock__image-element {
			opacity: 0.7;
			transform: scale(1.1);
		}

		.da-icontextblock__content {
			&::before,
			&::after {
				opacity: 1;
				transform: scale(1);
				transition-delay: 0.15s;
			}
		}

		.da-icontextblock__header,
		.da-icontextblock__body {
			opacity: 1;
			transform: translate3d(0, 0, 0);
			transition-delay: 0.15s;
		}

		.da-icontextblock__button {
			opacity: 1;
			transform: translateY(0);
			transition-delay: 0.3s;
		}
	}

	// Responsive adjustments
	@media (max-width: 575px) {
		.da-icontextblock__title {
			font-size: 1.3rem;
		}
	}

	@media (min-width: 576px) and (max-width: 767px) {
		.da-icontextblock__title {
			font-size: 1.3rem;
			font-family: Oswald;
		}
	}

	@media (min-width: 768px) and (max-width: 991px) {
		.da-icontextblock__title {
			font-size: 1.3rem;
		}
	}
}
