// Gravity Forms
.gform-theme--foundation .gform_fields {
	gap: 20px;
}

.gform-theme--foundation .gform-grid-col {
	padding-inline: 10px;
}

.gform_fields {
	.nice-select,
	select,
	input[type='email'],
	input[type='tel'],
	input[type='text'],
	textarea {
		border-radius: 4px;
		border: 1px;
		box-shadow: none;
		height: 48px;
		outline: none;
		border: 1px solid #cbcbcb;
		line-height: 48px;
	}
}

.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn)) > button:not([id*='mceu_']):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type='submit'], [type='button'], [type='reset']).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type='submit'], [type='button'], [type='reset']):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input[type='submit'].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) {
	width: 320px;
	height: 48px;
	border-radius: 6px;
	margin: 0 auto;
	background-color: #d45e2b;

	font-size: 16px;
	font-weight: 400;
	line-height: 19px;
	letter-spacing: 0.03em;
	text-align: center;
}

.gfield--width-full.shorten {
	width: 95%;
	margin: 0 auto;
	margin-top: 40px;

	.ginput_container.ginput_container_radio {
		padding-left: 20px;
	}
}

.gfield_label.gform-field-label {
	margin-bottom: 20px;
}

.gfield_radio {
	gap: 20px !important;
}

.gform-theme--framework .gfield--input-type-datepicker .ginput_container_date input {
	width: 100%;
}

.gform-theme--framework .gfield--type-choice.field_description_above.gfield--no-description .gform-field-label:where(:not([class*='gform-field-label--type-'])),
.gform-theme--framework .gfield--type-choice.field_description_below .gform-field-label:where(:not([class*='gform-field-label--type-'])) {
	font-size: 13px;
	font-weight: 400;
	line-height: 17px;
	letter-spacing: 0em;
	text-align: left;
	margin-bottom: 25px;
}
