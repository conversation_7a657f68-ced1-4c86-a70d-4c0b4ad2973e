// Define and apply a pattern for responsive styles targeting screens smaller than 1280px

// PATTERN RULES:
// property - value(unit) - media_query_condition - media-query_value
// Example
// .h-50-lt-1280 - means 50%
// .h-50px-lt-1280
//

// Screens Larger than 1280px
@media (min-width: 1280px) {
	.p-10px-gt-1280 {
		padding: 10px !important; // Adds 10px padding on all sides for smaller screens
	}
	.p-20px-gt-1280 {
		padding: 20px !important; // Adds 10px padding on all sides for smaller screens
	}
	.gap-20px-gt-1280 {
		gap: 20px !important;
	}
	.gap-100px-gt-1280 {
		gap: 100px !important;
	}
	.h-100-gt-1280 {
		height: 100% !important;
	}
}

// Screens Smaller than 1280px
@media (max-width: 1280px) {
	// ----------------------------------------------------------

	// <FONT-SIZE>
	.fs-18px-lt-1280 {
		font-size: 18px !important;
	}
	// </FONT-SIZE>

	// ----------------------------------------------------------

	// <PADDING>
	.pl-0-lt-1280 {
		padding-left: 0 !important; // Removes all padding on screens smaller than 1280px
	}

	// Padding 0 Less Than 1280px screen: Resets padding to 0 as part of responsive spacing adjustments
	.p-0-lt-1280 {
		padding: 0 !important; // Removes all padding on screens smaller than 1280px
	}

	.px-0-lt-1280 {
		padding-left: 0 !important; // Removes all padding on screens smaller than 1280px
		padding-right: 0 !important; // Removes all padding on screens smaller than 1280px
	}

	.px-20px-lt-1280 {
		padding-left: 20px !important; // Removes all padding on screens smaller than 1280px
		padding-right: 20px !important; // Removes all padding on screens smaller than 1280px
	}

	.py-0-lt-1280 {
		padding-top: 0 !important; // Removes all padding on screens smaller than 1280px
		padding-bottom: 0 !important; // Removes all padding on screens smaller than 1280px
	}

	.py-40px-lt-1280 {
		padding-top: 40px !important; // Removes all padding on screens smaller than 1280px
		padding-bottom: 40px !important; // Removes all padding on screens smaller than 1280px
	}

	// Padding 10 Less Than 1280px screen: Applies 10px padding on all sides for screens smaller than 1280px
	.p-10px-lt-1280 {
		padding: 10px !important; // Adds 10px padding on all sides for smaller screens
	}

	// Padding 20 Less Than 1280px screen: Applies 20px padding on all sides for screens smaller than 1280px
	.p-20px-lt-1280 {
		padding: 20px !important; // Adds 20px padding on all sides for smaller screens
	}

	// Padding 30 Less Than 1280px screen: Applies 30px padding on all sides for screens smaller than 1280px
	.p-30px-lt-1280 {
		padding: 30px !important; // Adds 30px padding on all sides for smaller screens
	}

	// Padding 40 Less Than 1280px screen: Applies 40px padding on all sides for screens smaller than 1280px
	.p-40px-lt-1280 {
		padding: 40px !important; // Adds 40px padding on all sides for smaller screens
	}

	// Padding 50 Less Than 1280px screen: Applies 50px padding on all sides for screens smaller than 1280px
	.p-50px-lt-1280 {
		padding: 50px !important; // Adds 50px padding on all sides for smaller screens
	}

	// Padding 60 Less Than 1280px screen: Applies 60px padding on all sides for screens smaller than 1280px
	.p-60px-lt-1280 {
		padding: 60px !important; // Adds 60px padding on all sides for smaller screens
	}
	// </PADDING>

	// ----------------------------------------------------------

	// <MARGIN>
	// y-axis
	.my-auto-lt-1280,
	.ml-auto-mr-auto-lt-1280 {
		margin-left: auto;
		margin-right: auto;
	}

	.mx-0-lt-1280 {
		margin-top: 0 !important;
		margin-bottom: 0 !important;
	}

	.mx-20px-lt-1280 {
		margin-top: 20px !important;
		margin-bottom: 20px !important;
	}

	.mx-40px-lt-1280 {
		margin-top: 40px !important;
		margin-bottom: 40px !important;
	}

	// MARGIN TOP
	// Margin 0 Less Than 1280px screen: Resets margin to 0 as part of responsive spacing adjustments
	.m-0-lt-1280 {
		margin: 0 !important; // Removes all margins on screens smaller than 1280px
	}
	.mt-30px-lt-1280 {
		margin-top: 30px !important;
	}
	.mt-40px-lt-1280 {
		margin-top: 40px !important;
	}

	// MARGIN BOTTOM

	.mb-0-lt-1280 {
		margin-bottom: 0 !important; // Ensures proper spacing on screens smaller than 1280px
	}

	// Margin Bottom 40 Less Than 1280px screen: Sets a consistent 40px bottom margin for responsive layouts
	.mb-40px-lt-1280 {
		margin-bottom: 40px !important; // Ensures proper spacing on screens smaller than 1280px
	}
	// </MARGIN>

	// ----------------------------------------------------------

	// Order 1 Less Than 1280px screen: Adjusts the visual order of elements to appear first in layout patterns
	.o-1-lt-1280 {
		order: 1 !important; // Positions this element as the first item on smaller screens
	}

	// Order 2 Less Than 1280px screen: Adjusts the visual order of elements to appear second in layout patterns
	.o-2-lt-1280 {
		order: 2 !important; // Positions this element as the second item on smaller screens
	}

	// ----------------------------------------------------------

	// <HEIGHT>
	.h-0-lt-1280 {
		height: 0 !important;
	}

	.h-20px-lt-1280 {
		height: 20px !important;
	}

	.h-40px-lt-1280 {
		height: 40px !important;
	}

	.h-60px-lt-1280 {
		height: 60px !important;
	}
	// </HEIGHT>

	// ----------------------------------------------------------

	// <GAP>
	.gap-0-lt-1280 {
		gap: 0 !important;
	}

	.gap-20px-lt-1280 {
		gap: 20px !important;
	}

	.gap-40px-lt-1280 {
		gap: 40px !important;
	}
	// </GAP>

	// ----------------------------------------------------------

	// </FLEX-DIRECTION>
	.fd-column-lt-1280 {
		flex-direction: column !important;
	}
	.fd-row-lt-1280 {
		flex-direction: row !important;
	}
	// </FLEX-DIRECTION>

	// ----------------------------------------------------------

	// <TEXT ALIGNMENT>
	.ta-left-lt-1280 {
		text-align: left !important;
	}
	.ta-center-lt-1280 {
		text-align: center !important;
	}
	.ta-right-lt-1280 {
		text-align: right !important;
	}
	// </TEXT ALIGNMENT>

	// ----------------------------------------------------------

	// <WIDTH>
	.w-60-lt-1280 {
		width: 60%;
	}
	.w-80-lt-1280 {
		width: 80%;
	}
	// </WIDTH>

	// ----------------------------------------------------------

	// <ALIGN ITEMS>
	.ai-fs-lt-1280 {
		align-items: flex-start !important;
	}
	// </ALIGN ITEMS>

	// <JUSTIFY CONTENT>
	.jc-fs-lt-1280 {
		justify-content: flex-start !important;
	}
	// </JUSTIFY CONTENT>

	// ----------------------------------------------------------

	// <ALIGN ITEMS>
	.gtc-1-1fr-lt-1280 {
		grid-template-columns: repeat(1, minmax(0, 1fr));
	}
	.gtc-2-1fr-lt-1280 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	// </ALIGN ITEMS>

	// ----------------------------------------------------------

	// <MAX WIDTH>
	.mw-unset-lt-1280 {
		max-width: unset !important;
	}
	// </MAX WIDTH>
}
