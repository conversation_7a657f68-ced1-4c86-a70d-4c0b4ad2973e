<?php

class FieldRenderer
{
	// Method to render different types of fields
	public static function renderT($args)
	{
		// var_dump($args);
	}

	public static function render($type, $id, $options = array())
	{
		switch ($type) {
			case 'page_select':
				return self::renderDropdown($id, $options);
				break;
				// Add cases for other field types as needed
			case 'text': // Add case for text type
				return self::renderText($id, $options);
				break;
			case 'checkbox': // Add case for text type
				return self::renderCheckbox($id, $options);
				break;
			case 'image_upload': // Add case for image upload
				return self::renderImageUpload($id, $options);
				break;
			case 'textarea': // Add case for textarea
				return self::renderTextarea($id, $options);
				break;
			case 'button': // Add case for button
				return self::renderButton($id, $options);
				break;
			default:
				return '';
				break;
		}
	}

	private static function renderImageUpload($id, $options)
	{
		$name = isset($options['name']) ? $options['name'] : $id;
		$section = isset($options['section']) ? $options['section'] : '';
		$value = get_option($name);

		if (!empty($name) && !empty($section)) {
			$name = $section . '[' . $name . ']';
		}

		$html = '<input type="text" name="' . $name . '" id="' . $id . '_image" value="' . esc_attr($value) . '" class="large regular-text">';
		$html .= '<input type="button" class="button button-secondary" id="' . $id . '_button" value="Upload Image">';
		$html .= '<br><img id="' . $id . '_preview" src="' . esc_url($value) . '" style="max-width: 200px; display: ' . ($value ? 'block' : 'none') . ';">';

		$html .= '<script>
		jQuery(document).ready(function($){
			$("#' . $id . '_button").click(function(e) {
				e.preventDefault();
				var custom_uploader = wp.media({
					title: "Select Image",
					button: {
						text: "Use this image"
					},
					multiple: false
				})
				.on("select", function() {
					var attachment = custom_uploader.state().get("selection").first().toJSON();
					$("#' . $id . '_image").val(attachment.url);
					$("#' . $id . '_preview").attr("src", attachment.url).show();
				})
				.open();
			});
		});
		</script>';

		echo $html;
	}

	private static function renderCheckbox($id, $options)
	{
		$name = isset($options['name']) ? $options['name'] : $id;
		$section = isset($options['section']) ? $options['section'] : '';
		$checked = get_option($name);
		$description = isset($options['description']) ? $options['description'] : '';

		// Check if both field ID and section ID are provided
		if (!empty($name) && !empty($section)) {
			// Concatenate section ID with field ID to form the name attribute
			$name = $section . '[' . $name . ']';
		}

		$html = '<input type="checkbox" name="' . $name . '" value="1" ' . checked(1, $checked, false) . '>';
		if (!empty($description)) {
			$html .= '<p class="description">' . esc_html($description) . '</p>';
		}
		echo $html;
	}

	// Method to render text field
	private static function renderText($id, $options)
	{
		$name = isset($options['name']) ? $options['name'] : $id;
		$section = isset($options['section']) ? $options['section'] : '';
		$value = get_option($name);

		$html = '<input type="text" name="' . $name . '" value="' . esc_attr($value) . '" class="large regular-text">';
		echo $html;
	}

	// Method to render textarea field
	private static function renderTextarea($id, $options)
	{
		$name = isset($options['name']) ? $options['name'] : $id;
		$section = isset($options['section']) ? $options['section'] : '';
		$value = get_option($name);
		$description = isset($options['description']) ? $options['description'] : '';

		$html = '<textarea name="' . $name . '" rows="10" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
		if (!empty($description)) {
			$html .= '<p class="description">' . esc_html($description) . '</p>';
		}
		echo $html;
	}

	// Method to render button field
	private static function renderButton($id, $options)
	{
		$button_text = isset($options['button_text']) ? $options['button_text'] : 'Click Me';
		$button_action = isset($options['button_action']) ? $options['button_action'] : '';
		$description = isset($options['description']) ? $options['description'] : '';

		$html = '<button type="button" class="button button-primary" id="' . esc_attr($id) . '" data-action="' . esc_attr($button_action) . '">' . esc_html($button_text) . '</button>';
		$html .= '<span id="' . esc_attr($id) . '_status" style="margin-left: 10px;"></span>';

		if (!empty($description)) {
			$html .= '<p class="description">' . esc_html($description) . '</p>';
		}

		// Add JavaScript for AJAX handling
		$html .= '<script>
		jQuery(document).ready(function($) {
			$("#' . esc_js($id) . '").click(function() {
				var button = $(this);
				var status = $("#' . esc_js($id) . '_status");
				var action = button.data("action");

				button.prop("disabled", true).text("Creating...");
				status.html("");

				$.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: "waw_" + action,
						nonce: "' . wp_create_nonce('waw_dev_tools_nonce') . '"
					},
					success: function(response) {
						if (response.success) {
							status.html("<span style=\"color: green;\">✓ " + response.data + "</span>");
						} else {
							status.html("<span style=\"color: red;\">✗ " + response.data + "</span>");
						}
						button.prop("disabled", false).text("' . esc_js($button_text) . '");
					},
					error: function() {
						status.html("<span style=\"color: red;\">✗ Error occurred</span>");
						button.prop("disabled", false).text("' . esc_js($button_text) . '");
					}
				});
			});
		});
		</script>';

		echo $html;
	}

	// Method to render dropdown selector for selecting a page
	private static function renderDropdown($id, $options)
	{
		$name = isset($options['name']) ? $options['name'] : $id;
		$section = isset($options['section']) ? $options['section'] : '';
		$selected = get_option($name);


		// Check if both field ID and section ID are provided
		if (!empty($name) && !empty($section)) {
			// Concatenate section ID with field ID to form the name attribute
			$name = $section . '[' . $name . ']';
		}

		$pages = get_pages();
		$html = '<select name="' . $name . '">';
		$html .= '<option value="">Select a Page</option>';
		foreach ($pages as $page) {
			$html .= '<option value="' . $page->ID . '" ' . selected($selected, $page->ID, false) . '>' . $page->post_title . '</option>';
		}
		$html .= '</select>';
		echo $html;
	}
}
