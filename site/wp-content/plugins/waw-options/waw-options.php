<?php
/*
 * Plugin Name: WAW Options Page
 * Description: Adds a custom options page with page selectors for Privacy Policy and Terms pages.
 * Version: 1.0.1
 * Author:       Welcome Agency
 * Author URI:   https://wearewelcome.com.au
 * License:      Commercial
 * License URI:  https://wearewelcome.com.au
 */


if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

require_once(plugin_dir_path(__FILE__) . 'includes/Render.php');


class WAW_Options
{
	private $settings_sections = array();
	private $settings_fields = array();
	private $tabs = array();

	// Method to initialize the options page
	public function init()
	{
		// Add hooks to add the plugin page and initialize settings sections and fields
		add_action('admin_menu', array($this, 'add_plugin_page'));
		add_action('admin_init', array($this, 'page_init'));
		add_action('admin_init', array($this, 'client_friendly_dashboard'), 999);
		add_action('admin_enqueue_scripts', array($this, 'enqueue_media_uploader'));
		add_action('init', array($this, 'dev_tools_init'));
		add_action('wp_ajax_waw_create_dummy_posts', array($this, 'ajax_create_dummy_posts'));
	}

	function enqueue_media_uploader()
	{
		wp_enqueue_media();
		wp_enqueue_script('jquery');
	}


	function client_friendly_dashboard()
	{
		// Check if the client-friendly dashboard mode is enabled
		$client_friendly_dashboard = get_option('client_friendly_dashboard');

		// Only hide items if client-friendly dashboard mode is enabled
		if ($client_friendly_dashboard) {
			// Remove WP Activity Log menu
			remove_menu_page('wsal-auditlog');

			// Remove ACF PRO menu
			remove_menu_page('edit.php?post_type=acf-field-group');

			// Remove WP Engine menu
			remove_menu_page('wpengine-common');
		}
	}

	function dev_tools_init()
	{
		// Enable debug mode
		if (get_option('enable_debug_mode')) {
			if (!defined('WP_DEBUG')) {
				define('WP_DEBUG', true);
			}
			if (!defined('WP_DEBUG_LOG')) {
				define('WP_DEBUG_LOG', true);
			}
			if (!defined('WP_DEBUG_DISPLAY')) {
				define('WP_DEBUG_DISPLAY', true);
			}
		}

		// Disable core block patterns
		if (get_option('disable_core_patterns')) {
			add_action('init', array($this, 'remove_core_patterns'));
		}

		// Enable query debug
		if (get_option('enable_query_debug')) {
			add_action('wp_footer', array($this, 'show_query_debug'));
			add_action('admin_footer', array($this, 'show_query_debug'));
		}

		// Add custom admin CSS
		if (get_option('custom_admin_css')) {
			add_action('admin_head', array($this, 'add_custom_admin_css'));
		}

		// Show template information
		if (get_option('show_template_info')) {
			add_action('admin_bar_menu', array($this, 'add_template_info_to_admin_bar'), 100);
		}
	}

	function remove_core_patterns()
	{
		$core_block_patterns = array(
			'query-standard-posts',
			'query-medium-posts',
			'query-small-posts',
			'query-grid-posts',
			'query-large-title-posts',
			'query-offset-posts',
			'social-links-shared-background-color',
		);

		foreach ($core_block_patterns as $core_block_pattern) {
			unregister_block_pattern("core/{$core_block_pattern}");
		}
		remove_theme_support('core-block-patterns');
	}

	function show_query_debug()
	{
		if (current_user_can('manage_options')) {
			global $wpdb;
			echo '<div style="background: #f1f1f1; padding: 10px; margin: 10px; border: 1px solid #ccc;">';
			echo '<h4>Query Debug Info</h4>';
			echo '<p><strong>Total Queries:</strong> ' . get_num_queries() . '</p>';
			echo '<p><strong>Query Time:</strong> ' . timer_stop() . ' seconds</p>';
			echo '<p><strong>Memory Usage:</strong> ' . size_format(memory_get_peak_usage()) . '</p>';
			echo '</div>';
		}
	}

	function add_custom_admin_css()
	{
		$custom_css = get_option('custom_admin_css');
		if (!empty($custom_css)) {
			echo '<style type="text/css">' . wp_strip_all_tags($custom_css) . '</style>';
		}
	}

	function add_template_info_to_admin_bar($wp_admin_bar)
	{
		if (!current_user_can('manage_options') || is_admin()) {
			return;
		}

		global $template;
		$template_name = basename($template);
		$template_path = str_replace(ABSPATH, '', $template);

		$wp_admin_bar->add_node(array(
			'id'    => 'template-info',
			'title' => 'Template: ' . $template_name,
			'href'  => '#',
			'meta'  => array(
				'title' => 'Current template: ' . $template_path,
			),
		));
	}

	function ajax_create_dummy_posts()
	{
		// Check nonce for security
		if (!wp_verify_nonce($_POST['nonce'], 'waw_dev_tools_nonce')) {
			wp_die('Security check failed');
		}

		// Check user permissions
		if (!current_user_can('manage_options')) {
			wp_die('Insufficient permissions');
		}

		$created_posts = 0;
		$dummy_posts = array(
			array(
				'title' => 'The Future of Web Development: Trends to Watch',
				'content' => 'Web development is constantly evolving, with new technologies and frameworks emerging regularly. In this post, we explore the latest trends that are shaping the future of web development, including progressive web apps, serverless architecture, and AI-powered development tools. These innovations are not only changing how we build websites but also how users interact with them.',
				'excerpt' => 'Discover the cutting-edge trends that are revolutionizing web development and what they mean for developers and businesses alike.'
			),
			array(
				'title' => 'Mastering Responsive Design: Best Practices for 2024',
				'content' => 'Responsive design has become a cornerstone of modern web development. With the increasing variety of devices and screen sizes, creating websites that work seamlessly across all platforms is more important than ever. This comprehensive guide covers the latest techniques, CSS Grid, Flexbox, and mobile-first approaches that will help you create stunning responsive websites.',
				'excerpt' => 'Learn the essential techniques and best practices for creating responsive websites that look great on any device.'
			),
			array(
				'title' => 'WordPress Security: Protecting Your Site from Threats',
				'content' => 'WordPress powers over 40% of the web, making it a popular target for hackers and malicious attacks. In this detailed guide, we cover essential security measures every WordPress site owner should implement, including strong passwords, security plugins, regular updates, and backup strategies. Protecting your website is not just about technology—it\'s about peace of mind.',
				'excerpt' => 'Essential security tips and strategies to keep your WordPress website safe from cyber threats and attacks.'
			),
			array(
				'title' => 'The Art of User Experience: Designing for Humans',
				'content' => 'Great user experience design goes beyond aesthetics—it\'s about understanding human behavior and creating intuitive interfaces that users love. This post explores the principles of UX design, user research methods, and practical tips for creating websites that not only look beautiful but also provide exceptional user experiences that drive engagement and conversions.',
				'excerpt' => 'Explore the fundamental principles of UX design and learn how to create user-centered websites that truly connect with your audience.'
			),
			array(
				'title' => 'Performance Optimization: Making Your Website Lightning Fast',
				'content' => 'Website speed is crucial for user experience and search engine rankings. Slow websites lose visitors and revenue. In this comprehensive guide, we dive deep into performance optimization techniques, including image optimization, caching strategies, code minification, and server optimization. Learn how to make your website load faster and keep your users engaged.',
				'excerpt' => 'Discover proven techniques to optimize your website\'s performance and deliver lightning-fast loading times for better user experience.'
			),
			array(
				'title' => 'Building Accessible Websites: Inclusive Design for Everyone',
				'content' => 'Web accessibility ensures that websites are usable by people with disabilities, but it benefits everyone. This guide covers the Web Content Accessibility Guidelines (WCAG), practical implementation tips, and tools for testing accessibility. Creating inclusive websites is not just the right thing to do—it\'s also good for business and SEO.',
				'excerpt' => 'Learn how to build accessible websites that provide equal access and opportunity to users with disabilities.'
			)
		);

		foreach ($dummy_posts as $post_data) {
			$post_id = wp_insert_post(array(
				'post_title'    => $post_data['title'],
				'post_content'  => $post_data['content'],
				'post_excerpt'  => $post_data['excerpt'],
				'post_status'   => 'publish',
				'post_type'     => 'post',
				'post_author'   => get_current_user_id(),
				'post_date'     => date('Y-m-d H:i:s', strtotime('-' . wp_rand(1, 30) . ' days')),
			));

			if ($post_id && !is_wp_error($post_id)) {
				$created_posts++;

				// Add some random tags
				$tags = array('web development', 'design', 'wordpress', 'tutorial', 'tips', 'best practices');
				wp_set_post_tags($post_id, array_slice($tags, 0, wp_rand(2, 4)));

				// Set a random category (assuming 'Uncategorized' exists)
				$categories = get_categories(array('hide_empty' => false));
				if (!empty($categories)) {
					wp_set_post_categories($post_id, array($categories[0]->term_id));
				}
			}
		}

		if ($created_posts > 0) {
			wp_send_json_success("Successfully created {$created_posts} dummy posts!");
		} else {
			wp_send_json_error("Failed to create dummy posts. Please try again.");
		}
	}

	public function add_plugin_page()
	{
		add_menu_page(
			'WAW Options',          // Page title
			'WAW Options',          // Menu title
			'manage_options',       // Capability
			'waw-options',          // Menu slug
			array($this, 'render'),  // Callback function to render the page
			'data:image/svg+xml;base64,' . base64_encode('<svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g data-name="Layer 2"><g data-name="options"><rect width="24" height="24" opacity="0"/><path d="M7 14.18V3a1 1 0 0 0-2 0v11.18a3 3 0 0 0 0 5.64V21a1 1 0 0 0 2 0v-1.18a3 3 0 0 0 0-5.64z"/><path d="M21 13a3 3 0 0 0-2-2.82V3a1 1 0 0 0-2 0v7.18a3 3 0 0 0 0 5.64V21a1 1 0 0 0 2 0v-5.18A3 3 0 0 0 21 13z"/><path d="M15 5a3 3 0 1 0-4 2.82V21a1 1 0 0 0 2 0V7.82A3 3 0 0 0 15 5z"/></g></g></svg>'),
			99
		);
	}
	// Method to render the options page
	public function render()
	{
		if (!current_user_can('manage_options')) {
			return;
		}

		$active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'general';

?>
		<div class="wrap">
			<h1><?php echo esc_html(get_admin_page_title()); ?></h1>
			<?php $this->render_tabs(); ?>

			<?php

			if ($active_tab == 'general') {
				include plugin_dir_path(__FILE__) . 'tabs/general-tab.php';
			} elseif ($active_tab == 'google') {
				include plugin_dir_path(__FILE__) . 'tabs/extra-tab.php';
			} elseif ($active_tab == 'logo') {
				include plugin_dir_path(__FILE__) . 'tabs/logo-tab.php';
			} elseif ($active_tab == 'dev-tools') {
				include plugin_dir_path(__FILE__) . 'tabs/dev-tools-tab.php';
			}

			?>

		</div>
<?php
	}

	public function page_init()
	{
		foreach ($this->settings_sections as $section) {
			// add_settings_section( string $id, string $title, callable $callback, string $page, array $args = array() )
			add_settings_section(
				$section['id'],
				$section['title'],
				isset($section['callback']) ? $section['callback'] : null,
				$section['page'],
			);
		}

		foreach ($this->settings_fields as $field) {
			add_settings_field(
				$field['id'],
				$field['title'],
				isset($field['callback']) ? $field['callback'] : null,
				$field['page'],
				$field['section'],
				$field['args']
			);

			register_setting(
				$field['group'],
				$field['id']
			);
		}
	}

	public function add_section($id, $title, $page)
	{
		$this->settings_sections[] = array(
			'id' => $id,
			'title' => $title,
			'page' => $page,
		);
	}

	// Method to add a field to the options page
	public function add_field($field)
	{

		$id = isset($field['id']) ? $field['id'] : '';
		$title = isset($field['title']) ? $field['title'] : '';
		$section = isset($field['section']) ? $field['section'] : '';
		$page = isset($field['page']) ? $field['page'] : 'waw-options-general';
		$type = isset($field['type']) ? $field['type'] : '';
		$args = isset($field['args']) ? $field['args'] : '';
		$group = isset($field['group']) ? $field['group'] : '';

		$this->settings_fields[] = array(
			'id' => $id,
			'title' => $title,
			'section' => $section,
			'page' => $page,
			'callback' => function ($args) use ($type, $id) {
				echo FieldRenderer::render($type, $id, $args);
			},
			'args' => $args,
			'group' => $group
		);
	}

	public function add_tab($id, $title)
	{
		$this->tabs[] = array(
			'id' => $id,
			'title' => $title,
		);
	}

	public function render_tabs()
	{
		if (empty($this->tabs)) {
			return;
		}

		$current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'general';

		echo '<h2 class="nav-tab-wrapper">';
		foreach ($this->tabs as $tab) {
			$active = $current_tab === $tab['id'] ? 'nav-tab-active' : '';
			echo '<a href="?page=waw-options&tab=' . $tab['id'] . '" class="nav-tab ' . $active . '">' . $tab['title'] . '</a>';
		}
		echo '</h2>';
	}
}

$waw_options_page = new WAW_Options();

// Add sections
$waw_options_page->add_section('general_settings_section', 'General Settings', 'waw-options-general');
$waw_options_page->add_section('logo_settings_section', 'Logo Settings', 'waw-options-logo');
$waw_options_page->add_section('extra_settings_section', 'Google Settings', 'waw-options-google');
$waw_options_page->add_section('dev_tools_settings_section', 'Developer Tools', 'waw-options-dev-tools');

// Add fields
// add_field($id, $title, $section, $type = null, $args = null)
$fields = array(
	// GENERAL
	array(
		'id' => 'privacy_policy_page',
		'title' => 'Privacy Policy',
		'section' => 'general_settings_section',
		'page' => 'waw-options-general',
		'type' => 'page_select',
		'group' => 'waw_options_group_general'
	),
	array(
		'id' => 'terms_page',
		'title' => 'Terms and Conditions',
		'section' => 'general_settings_section',
		'page' => 'waw-options-general',
		'type' => 'page_select',
		'group' => 'waw_options_group_general'
	),
	array(
		'id' => 'client_friendly_dashboard',
		'title' => 'Client-Friendly Dashboard',
		'section' => 'general_settings_section',
		'page' => 'waw-options-general',
		'type' => 'checkbox',
		'group' => 'waw_options_group_general'
	),
	// LOGO
	array(
		'id' => 'header_logo_light',
		'title' => 'Header Logo (Light)',
		'section' => 'logo_settings_section',
		'page' => 'waw-options-logo',
		'type' => 'image_upload',
		'group' => 'waw_options_group_logo'
	),
	array(
		'id' => 'header_logo_dark',
		'title' => 'Header Logo (Dark)',
		'section' => 'logo_settings_section',
		'page' => 'waw-options-logo',
		'type' => 'image_upload',
		'group' => 'waw_options_group_logo'
	),
	array(
		'id' => 'footer_logo_light',
		'title' => 'Footer Logo (Light)',
		'section' => 'logo_settings_section',
		'page' => 'waw-options-logo',
		'type' => 'image_upload',
		'group' => 'waw_options_group_logo'
	),
	array(
		'id' => 'footer_logo_dark',
		'title' => 'Footer Logo (Dark)',
		'section' => 'logo_settings_section',
		'page' => 'waw-options-logo',
		'type' => 'image_upload',
		'group' => 'waw_options_group_logo'
	),
	array(
		'id' => 'login_page_logo',
		'title' => 'Login Page Logo',
		'section' => 'logo_settings_section',
		'page' => 'waw-options-logo',
		'type' => 'image_upload',
		'group' => 'waw_options_group_logo'
	),
	// GOOGLE
	array(
		'id' => 'google_maps_api_key',
		'title' => 'Maps API Key',
		'section' => 'extra_settings_section',
		'page' => 'waw-options-google',
		'type' => 'text',
		'group' => 'waw_options_group_google'
	),
	array(
		'id' => 'google_recaptcha_api_key',
		'title' => 'reCAPTCHA API Key',
		'section' => 'extra_settings_section',
		'page' => 'waw-options-google',
		'type' => 'text',
		'group' => 'waw_options_group_google'
	),
	array(
		'id' => 'google_recaptcha_api_secret',
		'title' => 'reCAPTCHA API Secret',
		'section' => 'extra_settings_section',
		'page' => 'waw-options-google',
		'type' => 'text',
		'group' => 'waw_options_group_google'
	),
	// DEV TOOLS
	array(
		'id' => 'enable_debug_mode',
		'title' => 'Enable Debug Mode',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'checkbox',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Enables WP_DEBUG, WP_DEBUG_LOG, and WP_DEBUG_DISPLAY constants.'
		)
	),
	array(
		'id' => 'disable_core_patterns',
		'title' => 'Disable WordPress Core Block Patterns',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'checkbox',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Removes default WordPress query patterns from the block editor.'
		)
	),
	array(
		'id' => 'enable_query_debug',
		'title' => 'Enable Query Debug',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'checkbox',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Shows query count, execution time, and memory usage in footer (admin users only).'
		)
	),
	array(
		'id' => 'custom_admin_css',
		'title' => 'Custom Admin CSS',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'textarea',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Add custom CSS that will be loaded in the WordPress admin area.'
		)
	),
	array(
		'id' => 'show_template_info',
		'title' => 'Show Template Information',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'checkbox',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Display current template file information in the admin bar (admin users only).'
		)
	),
	array(
		'id' => 'create_dummy_posts',
		'title' => 'Create Dummy Posts',
		'section' => 'dev_tools_settings_section',
		'page' => 'waw-options-dev-tools',
		'type' => 'button',
		'group' => 'waw_options_group_dev_tools',
		'args' => array(
			'description' => 'Click to create 6 dummy blog posts for testing purposes.',
			'button_text' => 'Create 6 Dummy Posts',
			'button_action' => 'create_dummy_posts'
		)
	)
);

foreach ($fields as $field) {
	$waw_options_page->add_field($field);
}

// Add tabs
$waw_options_page->add_tab('general', 'General');
$waw_options_page->add_tab('logo', 'Logo');
$waw_options_page->add_tab('google', 'Google');
$waw_options_page->add_tab('dev-tools', 'Dev Tools');

// Initialize the options page
$waw_options_page->init();
