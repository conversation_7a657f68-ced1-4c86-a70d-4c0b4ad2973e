/**
 * External dependencies
 */
import classNames from 'classnames';
import './css/editor.scss';
import get_image from '../../../utils/get-image';
import hexToRGBA from '../../../utils/hex-to-rgba';

/**
 * WordPress dependencies
 */
import { __ } from '@wordpress/i18n';
import { PanelBody, PanelRow, Button, FocalPointPicker, BaseControl, RangeControl, ToggleControl } from '@wordpress/components';
import { InnerBlocks, InspectorControls, MediaUploadCheck, MediaUpload, PanelColorSettings, useBlockProps } from '@wordpress/block-editor';
import { __experimentalUnitControl as UnitControl } from '@wordpress/components';
/**
 * At the moment, deprecations don't handle create blocks from attributes
 * (like when using CPT templates). For this reason, this hook is necessary
 * to avoid breaking templates using the old quote block format.
 *
 * @param {Object} attributes Block attributes.
 * @param {string} clientId   Block client ID.
 */

export default function edit({ attributes, setAttributes }) {
	const { overlayImg, focalPoint, dimRatio, bg_color, contentBgColor, borderColor, enable_container, placeholder, showBorder, maxWidth, minWidth, enableShadow, isSticky, enableGrid, columnGap, columnSize, gx, hideOverflow, mobileNoPadding } = attributes;
	const blockProps = useBlockProps({
		className: classNames({
			'wawContainer-block__editor': true,
			'wawContainer--withGrid': enableGrid,
		}),
	});
	const units = [
		{ value: 'px', label: 'px', default: 0 },
		{ value: 'vw', label: 'vw', default: 100 },
	];
	return (
		<>
			{/* Sidebar */}
			<InspectorControls>
				<PanelColorSettings
					title={'Colors'}
					colorSettings={[
						{
							value: bg_color,
							onChange: (colorValue) => setAttributes({ bg_color: colorValue }),
							label: 'Background',
							enableAlpha: true,
						},
						{
							value: contentBgColor,
							onChange: (colorValue) => setAttributes({ contentBgColor: colorValue }),
							label: 'Content Background',
						},
						{
							value: borderColor,
							onChange: (colorValue) => setAttributes({ borderColor: colorValue }),
							label: 'Border Color',
						},
					]}
				></PanelColorSettings>
				<PanelBody title={__('Image Settings')}>
					<PanelRow>
						<MediaUploadCheck>
							<MediaUpload
								value={overlayImg}
								onSelect={(media) => {
									let img_url = media.sizes.full.url;
									setAttributes({ overlayImg: img_url });
								}}
								type="image"
								render={(open) => {
									return (
										<Button
											onClick={open.open}
											className="button"
										>
											Select background image
										</Button>
									);
								}}
							/>
						</MediaUploadCheck>
					</PanelRow>
					{overlayImg && (
						<PanelRow>
							<FocalPointPicker
								label={__('Focal point picker')}
								url={overlayImg}
								value={focalPoint}
								onChange={(newFocalPoint) => {
									setAttributes({
										focalPoint: newFocalPoint,
									});
								}}
							/>
						</PanelRow>
					)}
					<PanelRow>{get_image(overlayImg)}</PanelRow>
					{overlayImg && (
						<PanelRow>
							<Button
								variant="secondary"
								size="small"
								className="block-library-cover__reset-button"
								onClick={() =>
									setAttributes({
										overlayImg: undefined,
									})
								}
							>
								{__('Clear Media')}
							</Button>
						</PanelRow>
					)}
					<BaseControl label={__('Background Image Overlay', '@@text_domain')}>
						<RangeControl
							label={__('Opacity')}
							value={dimRatio}
							onChange={(newDimRation) =>
								setAttributes({
									dimRatio: newDimRation,
								})
							}
							min={0}
							max={1}
							step={0.01}
							required
						/>
					</BaseControl>
				</PanelBody>

				<PanelBody title={__('Container Settings')}>
					<PanelRow>
						<UnitControl
							label="Max Width"
							help="Constrict content area by setting the max width."
							onChange={(value) =>
								setAttributes({
									maxWidth: value,
								})
							}
							value={maxWidth}
							units={units}
						/>
					</PanelRow>
					<PanelRow>
						<UnitControl
							label="Min Width"
							help="Expand content area by setting the min width."
							onChange={(value) =>
								setAttributes({
									minWidth: value,
								})
							}
							value={minWidth}
							units={units}
						/>
					</PanelRow>
					<BaseControl label={__('Container Wrapper', '@@text_domain')}>
						<PanelRow>
							<ToggleControl
								label="Enable Container?"
								help={enable_container ? 'Container Enabled' : 'Container Disabled'}
								checked={enable_container}
								onChange={() => {
									setAttributes({ enable_container: !enable_container });
								}}
							/>
						</PanelRow>
						<PanelRow>
							<ToggleControl
								label="No Padding on mobile"
								help={mobileNoPadding ? 'Padding Removed' : 'Default Padding'}
								checked={mobileNoPadding}
								onChange={() => {
									setAttributes({ mobileNoPadding: !mobileNoPadding });
								}}
							/>
						</PanelRow>
						<PanelRow>
							<ToggleControl
								label="Add Border?"
								help={showBorder ? 'Border Added' : 'No Border'}
								checked={showBorder}
								onChange={() => {
									setAttributes({ showBorder: !showBorder });
								}}
							/>
						</PanelRow>
						<PanelRow>
							<ToggleControl
								label="Add Shadow?"
								help={enableShadow ? 'Shadow Enabled. Control via CSS.' : 'Shdow Disabled'}
								checked={enableShadow}
								onChange={() => {
									setAttributes({ enableShadow: !enableShadow });
								}}
							/>
						</PanelRow>
						<PanelRow>
							<ToggleControl
								label="Sticky?"
								help={isSticky ? 'Sticky Enabled' : 'Sticky Disabled'}
								checked={isSticky}
								onChange={() => {
									setAttributes({ isSticky: !isSticky });
								}}
							/>
						</PanelRow>
					</BaseControl>
				</PanelBody>

				<PanelBody title={__('Grid Settings')}>
					<BaseControl>
						<ToggleControl
							label="Enable Grid?"
							help={enableGrid ? 'Grid Enabled' : 'Grid Disabled'}
							checked={enableGrid}
							onChange={() => {
								setAttributes({ enableGrid: !enableGrid });
							}}
						/>
					</BaseControl>
					<BaseControl>
						<RangeControl
							disabled={!enableGrid}
							label={__('Columns')}
							value={columnSize}
							onChange={(value) => setAttributes({ columnSize: value })}
							min={1}
							max={12}
							step={1}
							required
						/>
					</BaseControl>
					<BaseControl>
						<UnitControl
							disabled={!enableGrid}
							label="Column Gap"
							onChange={(value) =>
								setAttributes({
									columnGap: value,
								})
							}
							value={columnGap}
							units={{ value: 'px', label: 'px', default: 0 }}
						/>
					</BaseControl>
				</PanelBody>

				<PanelBody title={__('Bootstrap Grid')}>
					<BaseControl>
						<RangeControl
							label={__('gx')}
							value={gx}
							onChange={(value) => setAttributes({ gx: value })}
							min={0}
							max={12}
							step={1}
							required
							help="Gutters are the padding between your columns, used to responsively space and align content in the Bootstrap grid system."
						/>
					</BaseControl>
					<BaseControl>
						<ToggleControl
							label="Hide Overflow?"
							help="The .container or .container-fluid parent may need to be adjusted if larger gutters are used too to avoid unwanted overflow. Add a wrapper around the .row with the .overflow-hidden class"
							checked={hideOverflow}
							onChange={() => {
								setAttributes({ hideOverflow: !hideOverflow });
							}}
						/>
					</BaseControl>
				</PanelBody>
			</InspectorControls>
			{/* Editor */}
			<div
				{...blockProps}
				style={{ backgroundColor: hexToRGBA(bg_color, 0.2) }}
				placeholder={placeholder || __('Type / to choose a block')}
			>
				<div
					className="wawContainer__content"
					style={{ backgroundColor: contentBgColor }}
				>
					<InnerBlocks />
				</div>
			</div>
		</>
	);
}
