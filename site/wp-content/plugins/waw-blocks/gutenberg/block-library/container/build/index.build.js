(()=>{var e={942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,o(n)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},t={};function n(a){var l=t[a];if(void 0!==l)return l.exports;var o=t[a]={exports:{}};return e[a](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.i18n,a=window.wp.blocks;var l=n(942),o=n.n(l);function r(e,t=1){if(!e)return e;let n=0,a=0,l=0;return 4===e.length?(n="0x"+e[1]+e[1],a="0x"+e[2]+e[2],l="0x"+e[3]+e[3]):7===e.length&&(n="0x"+e[1]+e[2],a="0x"+e[3]+e[4],l="0x"+e[5]+e[6]),"rgba("+ +n+","+ +a+","+ +l+","+t+")"}const i=window.wp.components,c=window.wp.blockEditor,d=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"waw-blocks/waw-container-block","title":"WAW Container Block","category":"text","description":"A block that mimics the behavior of Bootstrap\'s container class, providing a structured layout for organizing and containing other content within a defined space. It offers similar functionality to Bootstrap\'s container, allowing users to manage the width and alignment of content within a designated area.","keywords":["container","waw"],"textdomain":"default","attributes":{"placeholder":{"type":"string"},"overlayImg":{"type":"string","default":""},"bg_color":{"type":"string","default":""},"contentBgColor":{"type":"string","default":""},"borderColor":{"type":"string","default":""},"enable_container":{"type":"boolean","default":true},"enableShadow":{"type":"boolean","default":false},"isSticky":{"type":"boolean","default":false},"enableGrid":{"type":"boolean","default":false},"columnSize":{"type":"number","default":4},"columnGap":{"type":"string","default":"20px"},"showBorder":{"type":"boolean","default":false},"hideOverflow":{"type":"boolean","default":false},"mobileNoPadding":{"type":"boolean","default":false},"focalPoint":{"type":"object","default":{"x":0.5,"y":0.5}},"dimRatio":{"type":"number","default":0.5},"maxWidth":{"type":"string","default":""},"minWidth":{"type":"string","default":""},"gx":{"type":"number","default":0}},"example":{"attributes":{"bg_color":"#000"}},"supports":{"anchor":true,"html":false,"align":["wide","full"],"spacing":{"margin":true,"padding":true}},"styles":[{"name":"default","label":"Default","isDefault":true},{"name":"plain","label":"Plain"}],"editorScript":"file:../build/index.build.js","editorStyle":"file:../build/index.css","style":"file:../build/frontend.css"}');function s(e,t){if(!t)return{};const n=t[e];return n?{[`${e}Top`]:n.top?u(n.top):void 0,[`${e}Right`]:n.right?u(n.right):void 0,[`${e}Bottom`]:n.bottom?u(n.bottom):void 0,[`${e}Left`]:n.left?u(n.left):void 0}:{}}function u(e){return"string"==typeof e&&e.startsWith("var:preset|spacing|")?`var(--wp--preset--spacing--${e.split("|")[2]})`:e}const{name:m}=d;!function(e){if(!e)return;const{metadata:t,settings:n,name:l}=e;(0,a.registerBlockType)({name:l,...t},n)}({name:m,metadata:d,settings:{icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512"},(0,e.createElement)("path",{d:"M208 128C199.156 128 192 135.156 192 144V368C192 376.844 199.156 384 208 384S224 376.844 224 368V144C224 135.156 216.844 128 208 128ZM432 384C440.844 384 448 376.844 448 368V144C448 135.156 440.844 128 432 128S416 135.156 416 144V368C416 376.844 423.156 384 432 384ZM272 128C263.156 128 256 135.156 256 144V368C256 376.844 263.156 384 272 384S288 376.844 288 368V144C288 135.156 280.844 128 272 128ZM352 128C343.156 128 336 135.156 336 144V368C336 376.844 343.156 384 352 384S368 376.844 368 368V144C368 135.156 360.844 128 352 128ZM144 128C135.156 128 128 135.156 128 144V368C128 376.844 135.156 384 144 384S160 376.844 160 368V144C160 135.156 152.844 128 144 128ZM144 0H16C7.156 0 0 7.156 0 16V144C0 152.844 7.156 160 16 160S32 152.844 32 144V32H144C152.844 32 160 24.844 160 16S152.844 0 144 0ZM560 0H432C423.156 0 416 7.156 416 16S423.156 32 432 32H544V144C544 152.844 551.156 160 560 160S576 152.844 576 144V16C576 7.156 568.844 0 560 0ZM560 352C551.156 352 544 359.156 544 368V480H432C423.156 480 416 487.156 416 496S423.156 512 432 512H560C568.844 512 576 504.844 576 496V368C576 359.156 568.844 352 560 352ZM144 480H32V368C32 359.156 24.844 352 16 352S0 359.156 0 368V496C0 504.844 7.156 512 16 512H144C152.844 512 160 504.844 160 496S152.844 480 144 480Z"})),attributes:{...d.attributes},edit:function({attributes:n,setAttributes:a}){const{overlayImg:l,focalPoint:d,dimRatio:s,bg_color:u,contentBgColor:m,borderColor:g,enable_container:p,placeholder:b,showBorder:h,maxWidth:C,minWidth:f,enableShadow:w,isSticky:y,enableGrid:v,columnGap:E,columnSize:_,gx:x,hideOverflow:k,mobileNoPadding:S}=n,B=(0,c.useBlockProps)({className:o()({"wawContainer-block__editor":!0,"wawContainer--withGrid":v})}),P=[{value:"px",label:"px",default:0},{value:"vw",label:"vw",default:100}];return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.InspectorControls,null,(0,e.createElement)(c.PanelColorSettings,{title:"Colors",colorSettings:[{value:u,onChange:e=>a({bg_color:e}),label:"Background",enableAlpha:!0},{value:m,onChange:e=>a({contentBgColor:e}),label:"Content Background"},{value:g,onChange:e=>a({borderColor:e}),label:"Border Color"}]}),(0,e.createElement)(i.PanelBody,{title:(0,t.__)("Image Settings")},(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(c.MediaUploadCheck,null,(0,e.createElement)(c.MediaUpload,{value:l,onSelect:e=>{let t=e.sizes.full.url;a({overlayImg:t})},type:"image",render:t=>(0,e.createElement)(i.Button,{onClick:t.open,className:"button"},"Select background image")}))),l&&(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.FocalPointPicker,{label:(0,t.__)("Focal point picker"),url:l,value:d,onChange:e=>{a({focalPoint:e})}})),(0,e.createElement)(i.PanelRow,null,function(t){if(""!==t)return(0,e.createElement)("div",{className:"da-container__image-wrapper"},(0,e.createElement)("img",{className:"da-container__image",src:t}))}(l)),l&&(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.Button,{variant:"secondary",size:"small",className:"block-library-cover__reset-button",onClick:()=>a({overlayImg:void 0})},(0,t.__)("Clear Media"))),(0,e.createElement)(i.BaseControl,{label:(0,t.__)("Background Image Overlay","@@text_domain")},(0,e.createElement)(i.RangeControl,{label:(0,t.__)("Opacity"),value:s,onChange:e=>a({dimRatio:e}),min:0,max:1,step:.01,required:!0}))),(0,e.createElement)(i.PanelBody,{title:(0,t.__)("Container Settings")},(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.__experimentalUnitControl,{label:"Max Width",help:"Constrict content area by setting the max width.",onChange:e=>a({maxWidth:e}),value:C,units:P})),(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.__experimentalUnitControl,{label:"Min Width",help:"Expand content area by setting the min width.",onChange:e=>a({minWidth:e}),value:f,units:P})),(0,e.createElement)(i.BaseControl,{label:(0,t.__)("Container Wrapper","@@text_domain")},(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.ToggleControl,{label:"Enable Container?",help:p?"Container Enabled":"Container Disabled",checked:p,onChange:()=>{a({enable_container:!p})}})),(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.ToggleControl,{label:"No Padding on mobile",help:S?"Padding Removed":"Default Padding",checked:S,onChange:()=>{a({mobileNoPadding:!S})}})),(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.ToggleControl,{label:"Add Border?",help:h?"Border Added":"No Border",checked:h,onChange:()=>{a({showBorder:!h})}})),(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.ToggleControl,{label:"Add Shadow?",help:w?"Shadow Enabled. Control via CSS.":"Shdow Disabled",checked:w,onChange:()=>{a({enableShadow:!w})}})),(0,e.createElement)(i.PanelRow,null,(0,e.createElement)(i.ToggleControl,{label:"Sticky?",help:y?"Sticky Enabled":"Sticky Disabled",checked:y,onChange:()=>{a({isSticky:!y})}})))),(0,e.createElement)(i.PanelBody,{title:(0,t.__)("Grid Settings")},(0,e.createElement)(i.BaseControl,null,(0,e.createElement)(i.ToggleControl,{label:"Enable Grid?",help:v?"Grid Enabled":"Grid Disabled",checked:v,onChange:()=>{a({enableGrid:!v})}})),(0,e.createElement)(i.BaseControl,null,(0,e.createElement)(i.RangeControl,{disabled:!v,label:(0,t.__)("Columns"),value:_,onChange:e=>a({columnSize:e}),min:1,max:12,step:1,required:!0})),(0,e.createElement)(i.BaseControl,null,(0,e.createElement)(i.__experimentalUnitControl,{disabled:!v,label:"Column Gap",onChange:e=>a({columnGap:e}),value:E,units:{value:"px",label:"px",default:0}}))),(0,e.createElement)(i.PanelBody,{title:(0,t.__)("Bootstrap Grid")},(0,e.createElement)(i.BaseControl,null,(0,e.createElement)(i.RangeControl,{label:(0,t.__)("gx"),value:x,onChange:e=>a({gx:e}),min:0,max:12,step:1,required:!0,help:"Gutters are the padding between your columns, used to responsively space and align content in the Bootstrap grid system."})),(0,e.createElement)(i.BaseControl,null,(0,e.createElement)(i.ToggleControl,{label:"Hide Overflow?",help:"The .container or .container-fluid parent may need to be adjusted if larger gutters are used too to avoid unwanted overflow. Add a wrapper around the .row with the .overflow-hidden class",checked:k,onChange:()=>{a({hideOverflow:!k})}})))),(0,e.createElement)("div",{...B,style:{backgroundColor:r(u,.2)},placeholder:b||(0,t.__)("Type / to choose a block")},(0,e.createElement)("div",{className:"wawContainer__content",style:{backgroundColor:m}},(0,e.createElement)(c.InnerBlocks,null))))},save:function({attributes:t}){const{overlayImg:n,focalPoint:a,bg_color:l,contentBgColor:r,borderColor:i,dimRatio:d,showBorder:u,maxWidth:m,minWidth:g,enableShadow:p,isSticky:b,enableGrid:h,hideOverflow:C,mobileNoPadding:f}=t;let{className:w}=t;const y=n?{backgroundImage:`url(${n})`}:{},v=l?{backgroundColor:l}:{},E=r?{backgroundColor:r}:{},_=i?{borderColor:i}:{};let x;y.opacity=d,a&&(x=`${Math.round(100*a.x)}% ${Math.round(100*a.y)}%`,a&&(y.backgroundPosition=x)),w=o()("wawContainer",w,{"is-style-with-border":u,"is-with-shadow":p,"is-with-grid":h,"overflow-hidden":C,"no-padding":f,"is-sticky":b});const k={...m&&{maxWidth:m},...g&&{minWidth:g},...s("margin",t.style?.spacing),...s("padding",t.style?.spacing),..._};return(0,e.createElement)("div",{...c.useBlockProps.save({className:w,style:k})},l&&(0,e.createElement)("div",{className:"wawContainer__color-overlay",style:v}),n&&(0,e.createElement)("div",{className:"wawContainer__image-overlay",style:y}),(0,e.createElement)("div",{className:"wawContainer__content",style:E},function(t){const{enable_container:n,enableGrid:a,columnSize:l,columnGap:o,gx:r}=t,i={};if(a&&(i.display="grid",i.gridTemplateColumns=`repeat(${l}, 1fr)`,i.gridGap=o),n){let t=["container"],n=["row"];r&&(t.push(`gx-${r}`),n.push(`gx-${r}`));let a=t.join(" "),l=n.join(" ");return(0,e.createElement)("div",{className:`${a}`},(0,e.createElement)("div",{className:`${l}`},(0,e.createElement)("div",{className:"col",style:i},(0,e.createElement)(c.InnerBlocks.Content,null))))}return(0,e.createElement)("div",{className:"container-fluid"},(0,e.createElement)("div",{className:"row"},(0,e.createElement)("div",{className:"col",style:i},(0,e.createElement)(c.InnerBlocks.Content,null))))}(t)))}}}),(0,a.registerBlockStyle)(m,[{name:"with-radius",label:"With Radius"}])})()})();