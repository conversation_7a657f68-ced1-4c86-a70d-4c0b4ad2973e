(()=>{var e={655:(e,t)=>{var n;!function(){"use strict";var o=function(){function e(){}function t(e,t){for(var n=t.length,o=0;o<n;++o)a(e,t[o])}e.prototype=Object.create(null);var n={}.hasOwnProperty,o=/\s+/;function a(e,a){if(a){var l=typeof a;"string"===l?function(e,t){for(var n=t.split(o),a=n.length,l=0;l<a;++l)e[n[l]]=!0}(e,a):Array.isArray(a)?t(e,a):"object"===l?function(e,t){if(t.toString===Object.prototype.toString||t.toString.toString().includes("[native code]"))for(var o in t)n.call(t,o)&&(e[o]=!!t[o]);else e[t.toString()]=!0}(e,a):"number"===l&&function(e,t){e[t]=!0}(e,a)}}return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];var l=new e;t(l,o);var c=[];for(var r in l)l[r]&&c.push(r);return c.join(" ")}}();e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(o){var a=t[o];if(void 0!==a)return a.exports;var l=t[o]={exports:{}};return e[o](l,l.exports,n),l.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.wp.i18n,t=window.React,o=window.wp.primitives,a=(0,t.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,t.createElement)(o.Path,{d:"M13 6v6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H13zm-9 6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H4v6z"})),l=JSON.parse('{"name":"waw-blocks/accordion-parent","category":"common","supports":{"html":false,"anchor":true,"align":["wide","full"]},"example":{"attributes":{"preview":true}},"attributes":{"preview":{"type":"boolean","default":false},"iconOpen":{"type":"object","default":{}},"iconClosed":{"type":"object","default":{}},"iconColor":{"type":"string","default":""},"currentSlide":{"type":"number","default":0},"tabActive":{"type":"string","default":"item-1"},"tabsData":{"type":"array","default":[{"slug":"item-1","title":"Item 1"},{"slug":"item-2","title":"Item 2"}]}}}');var c=n(655),r=n.n(c);const i=window.wp.element,s=window.wp.blocks,m=window.wp.components,d=window.wp.blockEditor,u=window.wp.compose,p=window.wp.data,{Component:g}=wp.element,{__}=wp.i18n,{Button:v,Popover:_}=wp.components;class w extends g{constructor(){super(...arguments),this.state={confirmed:-1}}render(){const{onRemove:e,show:n,style:o,tooltipText:a=__("Remove Slide?","@@text_domain"),tooltipRemoveText:l=__("Remove","@@text_domain"),tooltipCancelText:c=__("Cancel","@@text_domain")}=this.props,{confirmed:r}=this.state;return n?(0,t.createElement)(v,{className:"wb-component-remove-button",onClick:()=>{-1===r&&this.setState({confirmed:0})},style:o},0===r?(0,t.createElement)(_,{className:"wb-component-remove-button-confirm",onClose:()=>{this.setState({confirmed:-1})},onClickOutside:()=>{this.setState({confirmed:-1})}},a,(0,t.createElement)(v,{className:"wb-component-remove-button-confirm-yep",onClick:e},l),(0,t.createElement)(v,{className:"wb-component-remove-button-confirm-nope",onClick:()=>{this.setState({confirmed:-1})}},c)):"",(0,t.createElement)("svg",{"aria-hidden":"true",focusable:"false","data-prefix":"fas","data-icon":"trash",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",class:"svg-inline--fa fa-trash fa-w-14 fa-3x"},(0,t.createElement)("path",{fill:"currentColor",d:"M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z",class:""}))):""}}function b(e){if(""!==e)return(0,t.createElement)("div",{className:"da-container__image-wrapper"},(0,t.createElement)("img",{className:"da-container__image",src:e}))}const f=(0,u.compose)([(0,p.withSelect)((e,t)=>{const{getBlock:n,isBlockSelected:o,hasSelectedInnerBlock:a}=e("core/block-editor"),{clientId:l}=t;return{clientId:l,block:n(l),isSelectedBlockInRoot:o(l)||a(l,!0)}}),(0,p.withDispatch)((e,t,n)=>{const{updateBlockAttributes:o,removeBlock:a,replaceInnerBlocks:l}=e("core/block-editor"),{getBlocks:c}=n.select("core/block-editor");return{replaceInnerBlocks:l,getBlocks:c,updateBlockAttributes:o,removeBlock:a,updateSlugsForInnerBlocks:e=>{let t=1;e.forEach((e,n)=>{o(e.clientId,{slug:`item-${t}`}),t++})}}})])(function(n){const{clientId:o,attributes:a,setAttributes:l,isSelectedBlockInRoot:c,getBlocks:u,replaceInnerBlocks:p,block:g,updateSlugsForInnerBlocks:v}=n;let{className:_=""}=n;const{tabActive:f,tabsData:E,iconColor:k,iconOpen:h,iconClosed:y}=a;_=r()(_,"waw-accordion__slides");let C=1;const S=(0,d.useBlockProps)(),I=u(o);(0,i.useEffect)(()=>{const e=g.innerBlocks.map(e=>e.attributes.slug),t=n.attributes.tabsData.map(e=>e.slug);let o=0;if(!N(e,t)){const e=g.innerBlocks.map((e,t)=>(o++,{title:B(E,e.attributes.slug),slug:`item-${o}`}));v(g.innerBlocks),l({tabsData:e})}},[I]);const B=(e,t)=>{const n=e.find(e=>e.slug===t);return n?n.title:null},N=(e,t)=>e.length===t.length&&e.every((e,n)=>e===t[n]),{preview:x}=a;return x?(0,t.createElement)("div",{className:"gutslider-preview"},(0,t.createElement)("img",{src:accordion_assets.banner,alt:"Preview"})):(0,t.createElement)(i.Fragment,null,(0,t.createElement)(d.InspectorControls,null,(0,t.createElement)(m.PanelBody,{title:(0,e.__)("Icon Settings"),initialOpen:!1},(0,t.createElement)(m.PanelRow,null,b(h.url)),(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(d.MediaUploadCheck,null,(0,t.createElement)(d.MediaUpload,{value:h,onSelect:e=>{l({iconOpen:{id:e.id,url:e?.sizes?.full?.url}})},type:"image",render:e=>(0,t.createElement)(m.Button,{onClick:e.open,className:"button"},"Expanded Icon")}))),(0,t.createElement)(m.PanelRow,null,b(y.url)),(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(d.MediaUploadCheck,null,(0,t.createElement)(d.MediaUpload,{value:y,onSelect:e=>{l({iconClosed:{id:e.id,url:e?.sizes?.full?.url}})},type:"image",render:e=>(0,t.createElement)(m.Button,{onClick:e.open,className:"button"},"Collapsed Icon")})))),(0,t.createElement)(d.PanelColorSettings,{title:"Colors",colorSettings:[{value:k,onChange:e=>l({iconColor:e}),label:"Icon Color"}]})),(0,t.createElement)("div",{...S,className:_,"data-tab-active":f},(0,t.createElement)("div",{className:"wb-tabs-buttons-wrapper"},(0,t.createElement)("div",{className:r()("wb-tabs-buttons")},E.map((o,a)=>{const{slug:i,title:s}=o,m=f===i;return(0,t.createElement)("div",{className:r()("wb-tabs-buttons-item",m?"wb-tabs-buttons-item-active":""),key:`tab_button_${a}`,onClick:()=>l({tabActive:i})},(0,t.createElement)("h4",null,"Item ",C++),(0,t.createElement)(w,{show:c,tooltipText:(0,e.__)("Remove item?","@@text_domain"),onRemove:()=>{(e=>{const{setAttributes:t,attributes:o,block:a,getBlocks:l,replaceInnerBlocks:c}=n,{tabsData:r=[]}=o;if(1>=a.innerBlocks.length)n.removeBlock(a.clientId);else if(a.innerBlocks[e]&&(n.removeBlock(a.innerBlocks[e].clientId),r[e])){const n=[...r];n.splice(e,1);const o=[...l(a.clientId)];o.splice(e,1),c(a.clientId,o,!1),t({tabsData:n})}})(a)}}))}),c?(0,t.createElement)(m.Tooltip,{text:(0,e.__)("Add Item","@@text_domain")},(0,t.createElement)(m.Button,{icon:"insert",onClick:()=>{let e=[];const t=E.length+1;e=[...E],e.push({slug:`slide-${t}`,title:`Tab ${t}`});const n=(0,s.createBlock)("waw-blocks/accordion-child",{slug:`slide-${t}`,title:`Tab ${t}`});let a=u(o);a=[...a,n],p(o,a,!1),l({tabsData:e})}})):""),(0,t.createElement)("div",{className:"waw-accordion__slide-content"},(0,t.createElement)(d.InnerBlocks,{template:(()=>{const{tabsData:e}=a;return e.map(e=>["waw-blocks/accordion-child",e])})(),allowedBlocks:["waw-blocks/accordion-child"]})))),(0,t.createElement)("style",null,`\n                    [data-block="${n.clientId}"] .waw-accordion__slides .waw-accordion__slide-content .block-editor-inner-blocks .block-editor-block-list__layout [data-type="waw-blocks/accordion-child"][data-tab="${f}"] {\n                        display: block;\n                    }\n                    `))}),{name:E}=l,k={...l,title:(0,e.__)("WAW Accordion","@@text_domain"),description:(0,e.__)("A single item for accordion","@@text_domain"),icon:a,edit:f,save:function({attributes:e}){const{iconClosed:n,iconOpen:o,iconColor:a}=e,l=d.useBlockProps.save({className:"wawAccordion da-accordion","data-icon-open":o.id,"data-icon-close":n.id,"data-icon-color":a});return(0,t.createElement)("div",{...l},(0,t.createElement)("div",{className:"da-accordion__wrapper"},(0,t.createElement)("div",{className:"da-accordion__container"},(0,t.createElement)(d.InnerBlocks.Content,null))))}},h=JSON.parse('{"name":"waw-blocks/accordion-child","parent":["waw-blocks/accordion-parent"],"category":"common","supports":{"html":false,"className":false,"anchor":true,"inserter":false,"reusable":false},"example":{},"attributes":{"parentId":{"type":"string"},"title":{"type":"string"},"slideImg":{"type":"string"},"slug":{"type":"string"},"iconImage":{"type":"string","default":""},"iconSvg":{"type":"string","default":""},"iconColor":{"type":"string","default":""},"iconType":{"type":"string","default":"none"},"iconSvgContent":{"type":"string","default":""}}}'),{name:y}=h,C={...h,title:(0,e.__)("Accordion Item","@@text_domain"),description:(0,e.__)("A single item for accordion","@@text_domain"),icon:a,getEditWrapperProps:e=>({"data-tab":e.slug}),edit:n=>{const{setAttributes:o,attributes:a,clientId:l}=n,{title:c,iconImage:s,iconSvg:u,iconColor:g,iconType:v,iconSvgContent:_}=a,w=(0,p.useSelect)(e=>(e("core/block-editor").getBlockParents(l),e("core/block-editor").getBlockOrder(l).length>0),[l]);let b=r()(n.className||"","waw-accordion__slide");return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(d.InspectorControls,null,(0,t.createElement)(m.PanelBody,{title:(0,e.__)("Icon Settings"),initialOpen:!1},(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(m.SelectControl,{label:(0,e.__)("Icon Type"),value:v,options:[{label:(0,e.__)("None"),value:"none"},{label:(0,e.__)("Image"),value:"image"},{label:(0,e.__)("SVG"),value:"svg"}],onChange:e=>o({iconType:e})})),"image"===v&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(d.MediaUploadCheck,null,(0,t.createElement)(d.MediaUpload,{onSelect:e=>o({iconImage:e.url}),allowedTypes:["image"],value:s,render:({open:n})=>(0,t.createElement)(m.Button,{onClick:n,variant:s?"secondary":"primary"},s?(0,e.__)("Change Image"):(0,e.__)("Select Image"))}))),s&&(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(m.Button,{onClick:()=>o({iconImage:""}),variant:"secondary",isDestructive:!0},(0,e.__)("Remove Image")))),"svg"===v&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(d.MediaUploadCheck,null,(0,t.createElement)(d.MediaUpload,{onSelect:async e=>{const t={iconImage:e.url,iconSvg:"",iconSvgContent:""};if(e.url&&e.url.toLowerCase().endsWith(".svg")){const n=await(async e=>{try{const t=await fetch(e);if(t.ok)return await t.text()}catch(e){console.error("Error fetching SVG:",e)}return""})(e.url);t.iconSvgContent=n}o(t)},allowedTypes:["image"],value:s,render:({open:n})=>(0,t.createElement)(m.Button,{onClick:n,variant:s?"secondary":"primary"},s?(0,e.__)("Change SVG File"):(0,e.__)("Select SVG File"))}))),s&&(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(m.Button,{onClick:()=>o({iconImage:"",iconSvgContent:""}),variant:"secondary",isDestructive:!0},(0,e.__)("Remove SVG File"))),(0,t.createElement)(m.PanelRow,null,(0,t.createElement)("hr",{style:{margin:"10px 0",opacity:.3}}),(0,t.createElement)("p",{style:{fontSize:"12px",color:"#666",margin:"5px 0"}},(0,e.__)("Or paste SVG code manually:"))),(0,t.createElement)(m.PanelRow,null,(0,t.createElement)(m.TextareaControl,{label:(0,e.__)("Manual SVG Code"),value:u,onChange:e=>{o({iconSvg:e,iconImage:e?"":s})},placeholder:(0,e.__)("Paste your SVG code here..."),rows:4})),(u||_)&&(0,t.createElement)(d.PanelColorSettings,{title:(0,e.__)("Icon Color"),colorSettings:[{value:g,onChange:e=>o({iconColor:e}),label:(0,e.__)("Icon Color")}]})))),(0,t.createElement)("div",{className:b},(0,t.createElement)("div",{className:"aa-accordion__title"},(()=>{if("image"===v&&s)return(0,t.createElement)("img",{className:"accordion-item__icon",src:s,alt:""});if("svg"===v){if(s&&s.toLowerCase().endsWith(".svg")&&_)return(0,t.createElement)("div",{className:"accordion-item__icon accordion-item__icon--svg",style:{color:g||void 0},dangerouslySetInnerHTML:{__html:_}});if(u)return(0,t.createElement)("div",{className:"accordion-item__icon accordion-item__icon--svg",style:{color:g||void 0},dangerouslySetInnerHTML:{__html:u}})}return null})(),(0,t.createElement)(d.RichText,{tagName:"span",placeholder:(0,e.__)("Accordion Title","@@text_domain"),value:c,onChange:e=>{o({title:e})},withoutInteractiveFormatting:!0,keepPlaceholderOnFocus:!0})),(0,t.createElement)(d.InnerBlocks,{templateLock:!1,renderAppender:w?void 0:()=>(0,t.createElement)(d.InnerBlocks.ButtonBlockAppender,null)})))},save:function(e){const{attributes:n}=e,{title:o,slug:a,iconImage:l,iconSvg:c,iconColor:r,iconType:i,iconSvgContent:s}=n,m=d.useBlockProps.save();return(0,t.createElement)("div",{...m,className:"da-accordion__item","data-tab":a},(0,t.createElement)("a",{href:"#",className:"da-accordion__item-heading"},(()=>{if("image"===i&&l)return(0,t.createElement)("img",{className:"da-accordion__item-icon",src:l,alt:""});if("svg"===i){if(l&&l.toLowerCase().endsWith(".svg")){if(s)return(0,t.createElement)("div",{className:"da-accordion__item-icon da-accordion__item-icon--svg",style:{color:r||void 0},dangerouslySetInnerHTML:{__html:s}});{const e=l.split("/"),n=e[e.length-1].replace(".svg","");return(0,t.createElement)("div",{className:"da-accordion__item-icon da-accordion__item-icon--svg da-accordion__item-icon--fetch-svg",style:{color:r||void 0},"data-svg-id":n})}}if(c)return(0,t.createElement)("div",{className:"da-accordion__item-icon da-accordion__item-icon--svg",style:{color:r||void 0},dangerouslySetInnerHTML:{__html:c}})}return null})(),(0,t.createElement)("span",{className:"da-accordion__item-title"},o),(0,t.createElement)("div",{className:"svg"},(0,t.createElement)("div",{className:"svg-close"}),(0,t.createElement)("div",{className:"svg-open"}))),(0,t.createElement)("div",{className:"da-accordion__item-content"},(0,t.createElement)(d.InnerBlocks.Content,null)))}};function S(e){if(!e)return;const{metadata:t,settings:n,name:o}=e;return(0,s.registerBlockType)({name:o,...t},n)}S({name:E,metadata:l,settings:k}),S({name:y,metadata:h,settings:C})})()})();