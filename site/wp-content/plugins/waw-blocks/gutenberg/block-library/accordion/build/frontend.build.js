(()=>{"use strict";new function(){var o=this;o.options={};var t=jQuery;o.init=function(){t(".wawAccordion").each(function(){var o=t(this),n=o.data("icon-close"),i=o.data("icon-open"),e=(o.data("icon-color"),o.find(".da-accordion__item:first-child > a"));e.addClass("active"),e.siblings(".da-accordion__item-content").slideDown(200),o.find(".da-accordion__item > a").on("click",function(n){n.preventDefault();var i=t(this);i.hasClass("active")?(i.removeClass("active"),i.siblings(".da-accordion__item-content").slideUp(200),o.find(".set > a i").removeClass("fa-minus").addClass("fa-plus")):(o.find(".set > a i").removeClass("fa-minus").addClass("fa-plus"),i.find("i").removeClass("fa-plus").addClass("fa-minus"),i.addClass("active"),o.find(".da-accordion__item-heading:not(.active) .da-accordion__item-content ").slideUp(200),i.siblings(".da-accordion__item-content").slideDown(200))});var a={};n&&(a[n]={type:"close",target:o}),i&&(a[i]={type:"open",target:o}),o.find(".da-accordion__item-icon--fetch-svg").each(function(){var o=t(this),n=o.data("svg-id");n&&(a[n]={type:"icon",target:o})}),Object.keys(a).forEach(function(o){var n=a[o];!function(o,n,i){console.log(`WAW Blocks: Fetching SVG ID ${o} for type ${n}`),fetch(`/wp-json/waw-blocks/v1/get-svg/${o}`).then(t=>(console.log(`WAW Blocks: SVG API response status: ${t.status} for ID ${o}`),t.text())).then(e=>{console.log(`WAW Blocks: SVG content received for ID ${o}:`,e.substring(0,100)+"..."),"icon"===n?(i.html(e),i.removeClass("da-accordion__item-icon--fetch-svg")):function(o,n,i){i.find(".svg-"+n).each(function(){t(this).html(o)})}(e,n,i)}).catch(t=>{console.error(`Error fetching ${n} SVG content for ID ${o}:`,t)})}(o,n.type,n.target)})})},t(document).ready(function(){try{o.init()}catch(o){console.warn("JS Error: "),console.log(o)}})}})();