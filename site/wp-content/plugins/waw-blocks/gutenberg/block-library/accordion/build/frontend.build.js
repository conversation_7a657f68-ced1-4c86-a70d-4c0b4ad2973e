(()=>{"use strict";new function(){var n=this;n.options={};var t=jQuery;function o(n,o,c){c.find(".svg-"+o).each(function(){t(this).html(n)})}n.init=function(){t(".wawAccordion").each(function(){var n=t(this),c=n.data("icon-close"),e=n.data("icon-open"),i=(n.data("icon-color"),n.find(".da-accordion__item:first-child > a"));i.addClass("active"),i.siblings(".da-accordion__item-content").slideDown(200),n.find(".da-accordion__item > a").on("click",function(o){o.preventDefault();var c=t(this);c.hasClass("active")?(c.removeClass("active"),c.siblings(".da-accordion__item-content").slideUp(200),n.find(".set > a i").removeClass("fa-minus").addClass("fa-plus")):(n.find(".set > a i").removeClass("fa-minus").addClass("fa-plus"),c.find("i").removeClass("fa-plus").addClass("fa-minus"),c.addClass("active"),n.find(".da-accordion__item-heading:not(.active) .da-accordion__item-content ").slideUp(200),c.siblings(".da-accordion__item-content").slideDown(200))}),c&&e&&function(n,t,c){fetch(`/wp-json/waw-blocks/v1/get-svg/${n}`).then(n=>n.text()).then(n=>{o(n,"close",c)}).catch(n=>{console.error("Error fetching close SVG content:",n)}),fetch(`/wp-json/waw-blocks/v1/get-svg/${t}`).then(n=>n.text()).then(n=>{o(n,"open",c)}).catch(n=>{console.error("Error fetching open SVG content:",n)})}(c,e,n),n.find(".da-accordion__item-icon--fetch-svg").each(function(){var n=t(this),o=n.data("svg-url");o&&function(n,t){fetch(n).then(n=>n.text()).then(n=>{t.html(n),t.removeClass("da-accordion__item-icon--fetch-svg")}).catch(n=>{console.error("Error fetching icon SVG content:",n)})}(o,n)})})},t(document).ready(function(){try{n.init()}catch(n){console.warn("JS Error: "),console.log(n)}})}})();