(()=>{"use strict";new function(){var i=this;i.options={};var n=jQuery;i.init=function(){n(".wawAccordion").each(function(){var i=n(this),t=i.data("icon-close"),a=i.data("icon-open"),e=(i.data("icon-color"),i.find(".da-accordion__item:first-child > a"));e.addClass("active"),e.siblings(".da-accordion__item-content").slideDown(200),i.find(".da-accordion__item > a").on("click",function(t){t.preventDefault();var a=n(this);a.hasClass("active")?(a.removeClass("active"),a.siblings(".da-accordion__item-content").slideUp(200),i.find(".set > a i").removeClass("fa-minus").addClass("fa-plus")):(i.find(".set > a i").removeClass("fa-minus").addClass("fa-plus"),a.find("i").removeClass("fa-plus").addClass("fa-minus"),a.addClass("active"),i.find(".da-accordion__item-heading:not(.active) .da-accordion__item-content ").slideUp(200),a.siblings(".da-accordion__item-content").slideDown(200))});var o={};t&&(o[t]={type:"close",target:i}),a&&(o[a]={type:"open",target:i}),i.find(".da-accordion__item-icon--fetch-svg").each(function(){var i=n(this),t=i.data("svg-id");t&&(o[t]={type:"icon",target:i})}),Object.keys(o).forEach(function(i){var t=o[i];!function(i,t,a){fetch(`/wp-json/waw-blocks/v1/get-svg/${i}`).then(i=>i.text()).then(i=>{"icon"===t?(a.html(i),a.removeClass("da-accordion__item-icon--fetch-svg")):function(i,t,a){a.find(".svg-"+t).each(function(){n(this).html(i)})}(i,t,a)}).catch(i=>{console.error(`Error fetching ${t} SVG content:`,i)})}(i,t.type,t.target)})})},n(document).ready(function(){try{i.init()}catch(i){console.warn("JS Error: "),console.log(i)}})}})();