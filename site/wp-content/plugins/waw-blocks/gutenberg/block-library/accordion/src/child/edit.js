/**
 * External dependencies
 */
import classnames from 'classnames/dedupe';

/**
 * WordPress dependencies
 */
import { __ } from '@wordpress/i18n';
import { Fragment } from '@wordpress/element';
import { useSelect } from '@wordpress/data';
import { RichText, InnerBlocks, InspectorControls, MediaUpload, MediaUploadCheck, PanelColorSettings } from '@wordpress/block-editor';
import { PanelBody, PanelRow, Button, SelectControl, TextareaControl } from '@wordpress/components';

/**
 * Block Edit Function.
 */
const edit = (props) => {
	const { setAttributes, attributes, clientId } = props;
	const { title, iconImage, iconSvg, iconColor, iconType, iconSvgContent } = attributes;

	const hasChildBlocks = useSelect(
		(select) => {
			const parentBlock = select('core/block-editor').getBlockParents(clientId);
			return select('core/block-editor').getBlockOrder(clientId).length > 0;
		},
		[clientId]
	);

	let className = classnames(props.className || '', 'waw-accordion__slide');

	// Function to fetch SVG content
	const fetchSvgContent = async (url) => {
		try {
			const response = await fetch(url);
			if (response.ok) {
				const svgContent = await response.text();
				return svgContent;
			}
		} catch (error) {
			console.error('Error fetching SVG:', error);
		}
		return '';
	};

	// Icon rendering functions
	const renderIcon = () => {
		if (iconType === 'image' && iconImage) {
			return (
				<img
					className="accordion-item__icon"
					src={iconImage}
					alt=""
				/>
			);
		} else if (iconType === 'svg') {
			// Handle SVG file from media library (use fetched content)
			if (iconImage && iconImage.toLowerCase().endsWith('.svg') && iconSvgContent) {
				return (
					<div
						className="accordion-item__icon accordion-item__icon--svg"
						style={{ color: iconColor || undefined }}
						dangerouslySetInnerHTML={{ __html: iconSvgContent }}
					/>
				);
			}
			// Handle manual SVG code
			else if (iconSvg) {
				return (
					<div
						className="accordion-item__icon accordion-item__icon--svg"
						style={{ color: iconColor || undefined }}
						dangerouslySetInnerHTML={{ __html: iconSvg }}
					/>
				);
			}
		}
		return null;
	};

	return (
		<Fragment>
			<InspectorControls>
				<PanelBody
					title={__('Icon Settings')}
					initialOpen={false}
				>
					<PanelRow>
						<SelectControl
							label={__('Icon Type')}
							value={iconType}
							options={[
								{ label: __('None'), value: 'none' },
								{ label: __('Image'), value: 'image' },
								{ label: __('SVG'), value: 'svg' },
							]}
							onChange={(value) => setAttributes({ iconType: value })}
						/>
					</PanelRow>

					{iconType === 'image' && (
						<>
							<PanelRow>
								<MediaUploadCheck>
									<MediaUpload
										onSelect={(media) => setAttributes({ iconImage: media.url })}
										allowedTypes={['image']}
										value={iconImage}
										render={({ open }) => (
											<Button
												onClick={open}
												variant={iconImage ? 'secondary' : 'primary'}
											>
												{iconImage ? __('Change Image') : __('Select Image')}
											</Button>
										)}
									/>
								</MediaUploadCheck>
							</PanelRow>
							{iconImage && (
								<PanelRow>
									<Button
										onClick={() => setAttributes({ iconImage: '' })}
										variant="secondary"
										isDestructive
									>
										{__('Remove Image')}
									</Button>
								</PanelRow>
							)}
						</>
					)}

					{iconType === 'svg' && (
						<>
							<PanelRow>
								<MediaUploadCheck>
									<MediaUpload
										onSelect={async (media) => {
											const newAttributes = {
												iconImage: media.url,
												iconSvg: '', // Clear manual SVG when file is selected
												iconSvgContent: '',
											};

											// If it's an SVG file, fetch its content
											if (media.url && media.url.toLowerCase().endsWith('.svg')) {
												const svgContent = await fetchSvgContent(media.url);
												newAttributes.iconSvgContent = svgContent;
											}

											setAttributes(newAttributes);
										}}
										allowedTypes={['image']}
										value={iconImage}
										render={({ open }) => (
											<Button
												onClick={open}
												variant={iconImage ? 'secondary' : 'primary'}
											>
												{iconImage ? __('Change SVG File') : __('Select SVG File')}
											</Button>
										)}
									/>
								</MediaUploadCheck>
							</PanelRow>
							{iconImage && (
								<PanelRow>
									<Button
										onClick={() =>
											setAttributes({
												iconImage: '',
												iconSvgContent: '',
											})
										}
										variant="secondary"
										isDestructive
									>
										{__('Remove SVG File')}
									</Button>
								</PanelRow>
							)}
							<PanelRow>
								<hr style={{ margin: '10px 0', opacity: 0.3 }} />
								<p style={{ fontSize: '12px', color: '#666', margin: '5px 0' }}>{__('Or paste SVG code manually:')}</p>
							</PanelRow>
							<PanelRow>
								<TextareaControl
									label={__('Manual SVG Code')}
									value={iconSvg}
									onChange={(value) => {
										setAttributes({
											iconSvg: value,
											iconImage: value ? '' : iconImage, // Clear file when manual code is entered
										});
									}}
									placeholder={__('Paste your SVG code here...')}
									rows={4}
								/>
							</PanelRow>
							{(iconSvg || iconSvgContent) && (
								<PanelColorSettings
									title={__('Icon Color')}
									colorSettings={[
										{
											value: iconColor,
											onChange: (color) => setAttributes({ iconColor: color }),
											label: __('Icon Color'),
										},
									]}
								/>
							)}
						</>
					)}
				</PanelBody>
			</InspectorControls>

			<div className={className}>
				<div className="aa-accordion__title">
					{renderIcon()}
					<RichText
						tagName="span"
						placeholder={__('Accordion Title', '@@text_domain')}
						value={title}
						onChange={(value) => {
							setAttributes({ title: value });
						}}
						withoutInteractiveFormatting
						keepPlaceholderOnFocus
					/>
				</div>
				<InnerBlocks
					templateLock={false}
					renderAppender={hasChildBlocks ? undefined : () => <InnerBlocks.ButtonBlockAppender />}
				/>
			</div>
		</Fragment>
	);
};

export default edit;
