[data-type='waw-blocks/accordion-parent'] {
	.wb-tabs-buttons-item {
		.waw-tabs-buttons-item-active {
			box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1) inset;
		}
		h4 {
			margin: 10px 0;
		}
	}
}
[data-type='waw-blocks/accordion-child'] {
	.waw_tabs__slide {
		padding: 10px;
		border: 2px dashed rgba(0, 0, 0, 0.1);
	}
}

.waw-accordion__slide {
	padding: 10px;
	border: 2px dashed rgba(0, 0, 0, 0.1);

	.waw-accordion__content {
		z-index: 5;
	}

	.block-editor-block-list__layout {
		flex-grow: 1;
	}

	.block-editor-inner-blocks {
		display: flex;
	}
}

.waw-accordion__slide-content > .block-editor-inner-blocks > .block-editor-block-list__layout > .block-editor-block-list__block > .block-editor-block-list__block-edit {
	margin-top: 12px;
}

.waw-accordion__slides {
	.wb-tabs-buttons {
		display: flex;
		flex-wrap: wrap;
	}
}
.waw-accordion__slides .wb-tabs-buttons.wb-tabs-buttons-align-start {
	justify-content: flex-start;
}
.waw-accordion__slides .wb-tabs-buttons.wb-tabs-buttons-align-center {
	justify-content: center;
}
.waw-accordion__slides .wb-tabs-buttons.wb-tabs-buttons-align-end {
	justify-content: flex-end;
}
.waw-accordion__slides .wb-tabs-buttons .edit-post-visual-editor,
.waw-accordion__slides .wb-tabs-buttons .edit-post-visual-editor p,
.waw-accordion__slides .wb-tabs-buttons .editor-rich-text__tinymce.mce-content-body {
	line-height: inherit;
}
.waw-accordion__slides .wb-tabs-buttons .wb-tabs-buttons-item {
	flex-basis: 25%;
	position: relative;
	display: block;
	padding: 8px 20px;
	margin-bottom: -1px;
	font-weight: 500;
	color: inherit;
	text-decoration: none;
	cursor: pointer;
	background-color: transparent;
	border: 1px solid transparent;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	box-shadow: none;
	opacity: 0.6;
	transition:
		0.15s border-color,
		0.15s background-color,
		0.15s opacity;
	will-change: border-color, background-color, opacity;
}
.waw-accordion__slides .wb-tabs-buttons .wb-tabs-buttons-item:hover {
	border-color: #dee2e6;
	opacity: 1;
}
.waw-accordion__slides .wb-tabs-buttons .wb-tabs-buttons-item.wb-tabs-buttons-item-active {
	background-color: rgba(255, 255, 255, 0.3);
	border-color: #dee2e6;
	border-bottom-color: #fff;
	opacity: 1;
}
.waw-accordion__slides .wb-tabs-buttons .wb-tabs-buttons-item:not(:hover) > .wb-component-remove-button {
	opacity: 0;
}
.waw-accordion__slides .waw-accordion__slide-content [data-type='waw-blocks/accordion-child'] {
	display: none;
}
/* <remove button> */
.wb-component-remove-button {
	position: absolute;
	align-items: center;
	justify-content: center;
	top: 0;
	right: 0;
	width: 30px;
	height: 30px;
	padding: 0;
	margin-top: -10px;
	margin-right: -10px;
	color: #fff;
	background-color: #000;
	border-radius: 50%;
	opacity: 0.7;
	transition:
		0.2s opacity,
		0.2s background-color;

	&:hover {
		background-color: #5c39a7;
		opacity: 1;
	}

	svg {
		position: absolute;
		width: auto;
		height: 0.8em;
	}
}

.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope {
	padding: 0;
	margin-left: 5px;
}
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep:hover,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep:focus,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope:hover,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope:focus {
	text-decoration: underline;
}
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep:focus,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope:focus {
	background: none;
	box-shadow: none;
}
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-yep:focus {
	color: #5c39a7;
}
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope,
.wb-component-remove-button-confirm .wb-component-remove-button-confirm-nope:focus {
	color: #999;
}
.wb-component-remove-button-confirm.components-popover::before {
	border-color: transparent;
}
.wb-component-remove-button-confirm.components-popover.is-top::after {
	border-top-color: #191e23;
}
.wb-component-remove-button-confirm.components-popover.is-bottom::after {
	border-bottom-color: #191e23;
}
.wb-component-remove-button-confirm .components-popover__content {
	padding: 4px 12px;
	color: #ffffff;
	white-space: nowrap;
	background: #191e23;
	border-width: 0;
}
.wb-component-remove-button-confirm:not(.is-mobile) .components-popover__content {
	min-width: 0;
}
.wb-component-remove-button-confirm .components-tooltip__shortcut {
	display: block;
	color: #7e8993;
	text-align: center;
}
/* </remove button> */

[data-type='tcntheme/aa-accordion-block-item'] .waw-accordion__slide {
	display: flex;
	flex-direction: column;
}
.waw-accordion__slide {
	.aa-accordion__title {
		background-color: rgba(0, 0, 0, 0.1);
		border-color: #dee2e6;
		border-bottom-color: #fff;
		opacity: 1;
		box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1) inset;
		padding: 12px 20px;
		box-sizing: border-box;
		margin-bottom: 10px;
		display: flex;
		align-items: center;
		gap: 12px;

		.accordion-item__icon {
			flex-shrink: 0;
			width: 24px;
			height: 24px;
			object-fit: contain;

			&--svg {
				display: flex;
				align-items: center;
				justify-content: center;

				svg {
					width: 100%;
					height: 100%;
					fill: currentColor;
				}
			}

			&--svg-file {
				// For SVG files loaded as images, we can't directly control fill color
				// but we can apply filters for basic color changes
				filter: brightness(1);

				// If color is specified, try to apply it (limited support)
				&[style*='color'] {
					// This is a fallback - SVG files as <img> have limited color control
					opacity: 0.8;
				}
			}
		}

		span {
			flex: 1;
		}
	}
}
