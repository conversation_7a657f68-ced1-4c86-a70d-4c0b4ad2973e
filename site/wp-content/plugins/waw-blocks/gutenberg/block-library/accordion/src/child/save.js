/**
 * External dependencies
 */
import classnames from 'classnames/dedupe';

/**
 * WordPress dependencies
 */
import { useSelect } from '@wordpress/data';

import { InnerBlocks, useBlockProps } from '@wordpress/block-editor';

/**
 * Block Save Function.
 */
function SaveComponent(props) {
	const { attributes } = props;
	const { title, slug, iconImage, iconSvg, iconColor, iconType } = attributes;

	let className = 'da-accordion__item';

	const blockProps = useBlockProps.save();

	// Icon rendering function
	const renderIcon = () => {
		if (iconType === 'image' && iconImage) {
			return (
				<img
					className="da-accordion__item-icon"
					src={iconImage}
					alt=""
				/>
			);
		} else if (iconType === 'svg') {
			// Handle SVG file from media library
			if (iconImage && iconImage.toLowerCase().endsWith('.svg')) {
				return (
					<img
						className="da-accordion__item-icon da-accordion__item-icon--svg-file"
						src={iconImage}
						alt=""
						style={{ color: iconColor || undefined }}
					/>
				);
			}
			// Handle manual SVG code
			else if (iconSvg) {
				return (
					<div
						className="da-accordion__item-icon da-accordion__item-icon--svg"
						style={{ color: iconColor || undefined }}
						dangerouslySetInnerHTML={{ __html: iconSvg }}
					/>
				);
			}
		}
		return null;
	};

	return (
		<div
			{...blockProps}
			className={className}
			data-tab={slug}
		>
			<a
				href="#"
				className="da-accordion__item-heading"
			>
				{renderIcon()}
				<span className="da-accordion__item-title">{title}</span>
				<div className="svg">
					<div className="svg-close"></div>
					<div className="svg-open"></div>
				</div>
			</a>
			<div className="da-accordion__item-content">
				<InnerBlocks.Content />
			</div>
		</div>
	);
}

export default SaveComponent;
