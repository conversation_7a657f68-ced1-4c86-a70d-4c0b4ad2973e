.accordion-container {
	position: relative;
	max-width: 500px;
	height: auto;
	margin: 10px auto;
}

.accordion-container > h2 {
	text-align: center;
	color: #fff;
	padding-bottom: 5px;
	margin-bottom: 20px;
	padding-bottom: 15px;
}

.da-accordion {
	&__item {
		position: relative;
		width: 100%;
		height: auto;
		border-bottom: 1px solid #ddd;

		&-heading {
			font-size: 20px;
			font-weight: 700;
			line-height: 29px;
			letter-spacing: 0em;
			text-align: left;
			display: flex;
			align-items: center;
			gap: 12px;

			.svg {
				position: absolute;
				right: 0;
				top: 50%;
				rotate: 0deg;
				width: 25px;
				height: 25px;
				transform: translate(0, -50%);

				svg {
					transition: all 0.2s ease-out;
					transform-origin: center;
					stroke: #d45e2b;
				}
			}
		}

		&-icon {
			flex-shrink: 0;
			width: 24px;
			height: 24px;
			object-fit: contain;

			&--svg {
				display: flex;
				align-items: center;
				justify-content: center;

				svg {
					width: 100%;
					height: 100%;
					fill: currentColor;
				}
			}

			&--svg-file {
				// For SVG files loaded as images, we can't directly control fill color
				// but we can apply filters for basic color changes
				filter: brightness(1);

				// If color is specified, try to apply it (limited support)
				&[style*='color'] {
					// This is a fallback - SVG files as <img> have limited color control
					opacity: 0.8;
				}
			}
		}

		&-title {
			flex: 1;
		}

		&-content {
			display: none;
			padding: 0 80px 40px 40px;

			p {
				padding: 20px 0;
				margin: 0;
			}
		}

		> a {
			position: relative;
			color: #111;
			display: block;
			font-size: 20px;
			font-weight: 700;
			padding: 20px 60px 20px 0;
			text-decoration: none;

			-webkit-transition: all 0.2s linear;
			-moz-transition: all 0.2s linear;
			transition: all 0.2s linear;

			.svg {
				display: flex;
				align-items: center;
				justify-content: center;

				.svg-close,
				.svg-open {
					position: absolute;
					line-height: 0;
					font-size: 0;
				}
			}
			.svg-close {
				opacity: 1;
				transition: opacity 0.35s ease-out;
			}
			.svg-open {
				opacity: 0;
				transition: opacity 0.35s ease-out;
			}

			&.active {
				.svg-close {
					opacity: 0;
					transition: opacity 0.35s ease-out;
				}
				.svg-open {
					opacity: 1;
					transition: opacity 0.35s ease-out;
				}
			}
		}
	}
}
