import './css/frontend.scss';

var waw_accordions = new (function () {
	var self = this;
	self.options = {};
	var $ = jQuery;

	self.init = function () {
		$('.wawAccordion').each(function () {
			var $accordion = $(this);
			var closeId = $accordion.data('icon-close');
			var openId = $accordion.data('icon-open');
			var color = $accordion.data('icon-color');
			var $firstItem = $accordion.find('.da-accordion__item:first-child > a');
			$firstItem.addClass('active');
			$firstItem.siblings('.da-accordion__item-content').slideDown(200);

			$accordion.find('.da-accordion__item > a').on('click', function (e) {
				e.preventDefault();
				var $this = $(this);
				if ($this.hasClass('active')) {
					$this.removeClass('active');
					$this.siblings('.da-accordion__item-content').slideUp(200);
					$accordion.find('.set > a i').removeClass('fa-minus').addClass('fa-plus');
				} else {
					$accordion.find('.set > a i').removeClass('fa-minus').addClass('fa-plus');
					$this.find('i').removeClass('fa-plus').addClass('fa-minus');
					$this.addClass('active');
					$accordion.find('.da-accordion__item-heading:not(.active) .da-accordion__item-content ').slideUp(200);
					$this.siblings('.da-accordion__item-content').slideDown(200);
				}
			});

			if (closeId && openId) {
				fetchSVGContent(closeId, openId, $accordion);
			}

			// Handle icon SVG fetching for accordion items
			$accordion.find('.da-accordion__item-icon--fetch-svg').each(function () {
				var $icon = $(this);
				var svgUrl = $icon.data('svg-url');
				if (svgUrl) {
					fetchIconSVG(svgUrl, $icon);
				}
			});
		});
	};

	function fetchSVGContent(closeId, openId, $accordion) {
		fetch(`/wp-json/waw-blocks/v1/get-svg/${closeId}`)
			.then((response) => response.text())
			.then((svgCloseContent) => {
				replaceSVGContent(svgCloseContent, 'close', $accordion);
			})
			.catch((error) => {
				console.error('Error fetching close SVG content:', error);
			});

		fetch(`/wp-json/waw-blocks/v1/get-svg/${openId}`)
			.then((response) => response.text())
			.then((svgOpenContent) => {
				replaceSVGContent(svgOpenContent, 'open', $accordion);
			})
			.catch((error) => {
				console.error('Error fetching open SVG content:', error);
			});
	}

	function replaceSVGContent(svgContent, type, $accordion) {
		$accordion.find('.svg-' + type).each(function () {
			// $(this).css('fill', color);
			$(this).html(svgContent);
		});
	}

	function fetchIconSVG(svgUrl, $icon) {
		fetch(svgUrl)
			.then((response) => response.text())
			.then((svgContent) => {
				$icon.html(svgContent);
				$icon.removeClass('da-accordion__item-icon--fetch-svg');
			})
			.catch((error) => {
				console.error('Error fetching icon SVG content:', error);
			});
	}

	$(document).ready(function () {
		try {
			self.init();
		} catch (e) {
			console.warn('JS Error: ');
			console.log(e);
		}
	});
})();
