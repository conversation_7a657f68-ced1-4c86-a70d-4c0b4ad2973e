import './css/frontend.scss';

var waw_accordions = new (function () {
	var self = this;
	self.options = {};
	var $ = jQuery;

	self.init = function () {
		$('.wawAccordion').each(function () {
			var $accordion = $(this);
			var closeId = $accordion.data('icon-close');
			var openId = $accordion.data('icon-open');
			var color = $accordion.data('icon-color');
			var $firstItem = $accordion.find('.da-accordion__item:first-child > a');
			$firstItem.addClass('active');
			$firstItem.siblings('.da-accordion__item-content').slideDown(200);

			$accordion.find('.da-accordion__item > a').on('click', function (e) {
				e.preventDefault();
				var $this = $(this);
				if ($this.hasClass('active')) {
					$this.removeClass('active');
					$this.siblings('.da-accordion__item-content').slideUp(200);
					$accordion.find('.set > a i').removeClass('fa-minus').addClass('fa-plus');
				} else {
					$accordion.find('.set > a i').removeClass('fa-minus').addClass('fa-plus');
					$this.find('i').removeClass('fa-plus').addClass('fa-minus');
					$this.addClass('active');
					$accordion.find('.da-accordion__item-heading:not(.active) .da-accordion__item-content ').slideUp(200);
					$this.siblings('.da-accordion__item-content').slideDown(200);
				}
			});

			// Collect all SVG IDs that need to be fetched
			var svgIds = {};

			// Add accordion open/close icons
			if (closeId) {
				svgIds[closeId] = { type: 'close', target: $accordion };
			}
			if (openId) {
				svgIds[openId] = { type: 'open', target: $accordion };
			}

			// Add accordion item icons
			$accordion.find('.da-accordion__item-icon--fetch-svg').each(function () {
				var $icon = $(this);
				var svgId = $icon.data('svg-id');
				if (svgId) {
					svgIds[svgId] = { type: 'icon', target: $icon };
				}
			});

			// Fetch all SVGs
			Object.keys(svgIds).forEach(function (svgId) {
				var svgData = svgIds[svgId];
				fetchSVGById(svgId, svgData.type, svgData.target);
			});
		});
	};

	function fetchSVGById(svgId, type, target) {
		console.log(`WAW Blocks: Fetching SVG ID ${svgId} for type ${type}`);
		fetch(`/wp-json/waw-blocks/v1/get-svg/${svgId}`)
			.then((response) => {
				console.log(`WAW Blocks: SVG API response status: ${response.status} for ID ${svgId}`);
				return response.text();
			})
			.then((svgContent) => {
				console.log(`WAW Blocks: SVG content received for ID ${svgId}:`, svgContent.substring(0, 100) + '...');
				if (type === 'icon') {
					// Handle accordion item icons
					target.html(svgContent);
					target.removeClass('da-accordion__item-icon--fetch-svg');
				} else {
					// Handle accordion open/close icons
					replaceSVGContent(svgContent, type, target);
				}
			})
			.catch((error) => {
				console.error(`Error fetching ${type} SVG content for ID ${svgId}:`, error);
			});
	}

	function replaceSVGContent(svgContent, type, $accordion) {
		$accordion.find('.svg-' + type).each(function () {
			// $(this).css('fill', color);
			$(this).html(svgContent);
		});
	}

	$(document).ready(function () {
		try {
			self.init();
		} catch (e) {
			console.warn('JS Error: ');
			console.log(e);
		}
	});
})();
