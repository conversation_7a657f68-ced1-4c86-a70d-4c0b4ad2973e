import { useBlockProps } from '@wordpress/block-editor';

export default function save(props) {
	const { attributes } = props;
	const { address, zoom, height, snazzyMapStyle, customPin } = attributes;

	const blockProps = useBlockProps.save({
		className: 'waw-google-map-block',
	});

	return (
		<div {...blockProps}>
			<div
				className="waw-google-map"
				style={{ height: `${height}px` }}
				data-address={address}
				data-zoom={zoom}
				data-snazzy-style={snazzyMapStyle}
				data-custom-pin={customPin}
			>
			</div>
		</div>
	);
}
