import React, { useState, useEffect, Fragment } from 'react';
import { 
	PanelRow, 
	PanelBody, 
	TextControl, 
	RangeControl, 
	TextareaControl,
	Button,
	Notice
} from '@wordpress/components';

import { 
	InspectorControls, 
	MediaUploadCheck, 
	MediaUpload, 
	useBlockProps 
} from '@wordpress/block-editor';

import { __ } from '@wordpress/i18n';
import classNames from 'classnames';

import './css/editor.scss';

export default function edit(props) {
	const { attributes, setAttributes } = props;
	const { address, zoom, height, snazzyMapStyle, customPin } = attributes;
	const [mapLoaded, setMapLoaded] = useState(false);
	const [mapError, setMapError] = useState('');

	const blockProps = useBlockProps({
		className: classNames({
			'waw-google-map-block': true,
		}),
	});

	// Check if Google Maps API key is available
	const apiKey = window.wawGoogleMaps?.apiKey || '';

	useEffect(() => {
		if (!apiKey) {
			setMapError('Google Maps API key not configured. Please add your API key in WAW Options > Google.');
			return;
		}

		if (address && window.google && window.google.maps) {
			initializeMap();
		} else if (address && !window.google) {
			loadGoogleMapsAPI();
		}
	}, [address, zoom, snazzyMapStyle, customPin, apiKey]);

	const loadGoogleMapsAPI = () => {
		if (window.google && window.google.maps) {
			initializeMap();
			return;
		}

		const script = document.createElement('script');
		script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
		script.async = true;
		script.defer = true;
		script.onload = () => {
			setMapLoaded(true);
			initializeMap();
		};
		script.onerror = () => {
			setMapError('Failed to load Google Maps API');
		};
		document.head.appendChild(script);
	};

	const initializeMap = () => {
		if (!address || !window.google || !window.google.maps) return;

		const mapElement = document.getElementById(`waw-map-editor-${props.clientId}`);
		if (!mapElement) return;

		const geocoder = new window.google.maps.Geocoder();
		
		geocoder.geocode({ address: address }, (results, status) => {
			if (status === 'OK' && results[0]) {
				const mapOptions = {
					center: results[0].geometry.location,
					zoom: zoom,
					mapTypeId: window.google.maps.MapTypeId.ROADMAP,
				};

				// Apply Snazzy Maps style if provided
				if (snazzyMapStyle) {
					try {
						const styleArray = JSON.parse(snazzyMapStyle);
						mapOptions.styles = styleArray;
					} catch (e) {
						console.warn('Invalid Snazzy Maps style JSON');
					}
				}

				const map = new window.google.maps.Map(mapElement, mapOptions);

				// Add marker
				const markerOptions = {
					position: results[0].geometry.location,
					map: map,
					title: address
				};

				// Use custom pin if provided
				if (customPin) {
					markerOptions.icon = {
						url: customPin,
						scaledSize: new window.google.maps.Size(40, 40)
					};
				}

				new window.google.maps.Marker(markerOptions);
				setMapError('');
			} else {
				setMapError('Geocoding failed: ' + status);
			}
		});
	};

	const renderMapPreview = () => {
		if (!apiKey) {
			return (
				<Notice status="error" isDismissible={false}>
					{mapError}
				</Notice>
			);
		}

		if (!address) {
			return (
				<div className="waw-map-placeholder">
					<div className="waw-map-placeholder-content">
						<span className="dashicons dashicons-location-alt"></span>
						<p>Enter an address in the block settings to display the map</p>
					</div>
				</div>
			);
		}

		if (mapError) {
			return (
				<Notice status="error" isDismissible={false}>
					{mapError}
				</Notice>
			);
		}

		return (
			<div 
				id={`waw-map-editor-${props.clientId}`}
				className="waw-google-map-preview"
				style={{ height: `${height}px`, width: '100%' }}
			>
				Loading map...
			</div>
		);
	};

	return (
		<Fragment>
			<InspectorControls>
				<PanelBody
					title={__('Map Settings')}
					initialOpen={true}
				>
					<PanelRow>
						<TextControl
							label="Address"
							value={address}
							onChange={(value) => setAttributes({ address: value })}
							placeholder="Enter address or location"
							help="Enter the address or location you want to display on the map"
						/>
					</PanelRow>
					<PanelRow>
						<RangeControl
							label="Zoom Level"
							value={zoom}
							onChange={(value) => setAttributes({ zoom: value })}
							min={1}
							max={20}
							help="Adjust the zoom level of the map (1 = world view, 20 = street level)"
						/>
					</PanelRow>
					<PanelRow>
						<RangeControl
							label="Map Height (px)"
							value={height}
							onChange={(value) => setAttributes({ height: value })}
							min={200}
							max={800}
							help="Set the height of the map in pixels"
						/>
					</PanelRow>
				</PanelBody>

				<PanelBody
					title={__('Styling')}
					initialOpen={false}
				>
					<PanelRow>
						<TextareaControl
							label="Snazzy Maps Style (JSON)"
							value={snazzyMapStyle}
							onChange={(value) => setAttributes({ snazzyMapStyle: value })}
							placeholder='[{"featureType":"all","elementType":"labels.text.fill","stylers":[{"saturation":36}]}]'
							help="Paste Snazzy Maps JSON style array to customize map appearance"
							rows={6}
						/>
					</PanelRow>
					<PanelRow>
						<MediaUploadCheck>
							<MediaUpload
								value={customPin}
								onSelect={(media) => {
									setAttributes({ customPin: media.url });
								}}
								type="image"
								render={(open) => {
									return (
										<Button
											onClick={open.open}
											className="button"
										>
											{customPin ? 'Change Custom Pin' : 'Select Custom Pin'}
										</Button>
									);
								}}
							/>
						</MediaUploadCheck>
					</PanelRow>
					{customPin && (
						<PanelRow>
							<div>
								<img src={customPin} style={{ maxWidth: '100px', height: 'auto' }} alt="Custom pin preview" />
								<br />
								<Button
									variant="secondary"
									size="small"
									onClick={() => setAttributes({ customPin: '' })}
								>
									{__('Remove Custom Pin')}
								</Button>
							</div>
						</PanelRow>
					)}
				</PanelBody>
			</InspectorControls>

			<div {...blockProps}>
				{renderMapPreview()}
			</div>
		</Fragment>
	);
}
