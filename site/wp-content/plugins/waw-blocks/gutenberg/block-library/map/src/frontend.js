import './css/frontend.scss';

document.addEventListener('DOMContentLoaded', function() {
	// Initialize all Google Maps on the page
	const mapElements = document.querySelectorAll('.waw-google-map');
	
	if (mapElements.length === 0) {
		return;
	}

	// Check if Google Maps API is available
	if (typeof window.google === 'undefined' || typeof window.google.maps === 'undefined') {
		console.warn('Google Maps API not loaded');
		return;
	}

	mapElements.forEach(function(mapElement) {
		initializeMap(mapElement);
	});
});

function initializeMap(mapElement) {
	const address = mapElement.getAttribute('data-address');
	const zoom = parseInt(mapElement.getAttribute('data-zoom')) || 15;
	const snazzyStyle = mapElement.getAttribute('data-snazzy-style');
	const customPin = mapElement.getAttribute('data-custom-pin');

	if (!address) {
		mapElement.innerHTML = '<div class="waw-map-error">No address specified</div>';
		return;
	}

	const geocoder = new window.google.maps.Geocoder();
	
	geocoder.geocode({ address: address }, function(results, status) {
		if (status === 'OK' && results[0]) {
			const mapOptions = {
				center: results[0].geometry.location,
				zoom: zoom,
				mapTypeId: window.google.maps.MapTypeId.ROADMAP,
			};

			// Apply Snazzy Maps style if provided
			if (snazzyStyle) {
				try {
					const styleArray = JSON.parse(snazzyStyle);
					mapOptions.styles = styleArray;
				} catch (e) {
					console.warn('Invalid Snazzy Maps style JSON:', e);
				}
			}

			const map = new window.google.maps.Map(mapElement, mapOptions);

			// Add marker
			const markerOptions = {
				position: results[0].geometry.location,
				map: map,
				title: address
			};

			// Use custom pin if provided
			if (customPin) {
				markerOptions.icon = {
					url: customPin,
					scaledSize: new window.google.maps.Size(40, 40)
				};
			}

			new window.google.maps.Marker(markerOptions);
		} else {
			mapElement.innerHTML = '<div class="waw-map-error">Failed to load map: ' + status + '</div>';
		}
	});
}
