.waw-google-map-block {
	margin: 20px 0;

	.waw-google-map {
		width: 100%;
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		// Ensure Google Maps controls are properly styled
		.gm-style {
			font-family: inherit;
		}

		// Custom info window styling
		.gm-style-iw {
			padding: 12px;
			border-radius: 4px;
		}
	}

	.waw-map-error {
		background-color: #f8d7da;
		color: #721c24;
		padding: 16px;
		border: 1px solid #f5c6cb;
		border-radius: 4px;
		text-align: center;
		font-size: 14px;
	}

	.waw-map-placeholder {
		background-color: #f8f9fa;
		color: #6c757d;
		padding: 40px;
		text-align: center;
		border: 2px dashed #dee2e6;
		border-radius: 8px;
		font-size: 14px;
	}
}

// Responsive design
@media (max-width: 768px) {
	.waw-google-map-block {
		.waw-google-map {
			border-radius: 4px;
		}
	}
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
	.waw-google-map-block {
		.waw-google-map {
			// Ensure crisp rendering on retina displays
			image-rendering: -webkit-optimize-contrast;
		}
	}
}
