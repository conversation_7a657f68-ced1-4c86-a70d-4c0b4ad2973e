/**
 * WordPress dependencies
 */
import { __ } from '@wordpress/i18n';
// Simple map icon SVG
const icon = (
	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="currentColor"/>
	</svg>
);

/**
 * Internal dependencies
 */
import initBlock from '../../../utils/init-block';
import edit from './edit';
import metadata from './block.json';
import save from './save';

const { name } = metadata;

export { metadata, name };

export const settings = {
	icon,
	attributes: {
		...metadata.attributes,
	},
	edit,
	save,
};

initBlock({ name, metadata, settings });
