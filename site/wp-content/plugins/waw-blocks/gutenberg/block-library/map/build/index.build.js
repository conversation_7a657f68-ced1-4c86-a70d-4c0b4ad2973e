(()=>{var e={942:(e,t)=>{var a;!function(){"use strict";var o={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var a=arguments[t];a&&(e=r(e,l(a)))}return e}function l(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var a in e)o.call(e,a)&&e[a]&&(t=r(t,a));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(a=function(){return n}.apply(t,[]))||(e.exports=a)}()}},t={};function a(o){var n=t[o];if(void 0!==n)return n.exports;var l=t[o]={exports:{}};return e[o](l,l.exports,a),l.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.i18n,o=window.wp.blocks,n=window.wp.components,l=window.wp.blockEditor;var r=a(942),s=a.n(r);const i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"waw-blocks/google-map","title":"WAW Google Map","category":"embed","description":"Display a Google Map with custom styling and pins","keywords":["map","google","location","address","waw"],"textdomain":"default","attributes":{"address":{"type":"string","default":""},"zoom":{"type":"number","default":15},"height":{"type":"number","default":400},"snazzyMapStyle":{"type":"string","default":""},"customPin":{"type":"string","default":""}},"supports":{"anchor":true,"html":false,"layout":{"allowEditing":false},"spacing":{"blockGap":true,"margin":true,"padding":true}},"editorScript":"file:../build/index.build.js","editorStyle":"file:../build/index.css","script":"file:../build/frontend.build.js","style":"file:../build/frontend.css"}'),c=(0,e.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z",fill:"currentColor"})),{name:d}=i;!function(e){if(!e)return;const{metadata:t,settings:a,name:n}=e;(0,o.registerBlockType)({name:n,...t},a)}({name:d,metadata:i,settings:{icon:c,attributes:{...i.attributes},edit:function(a){const{attributes:o,setAttributes:r}=a,{address:i,zoom:c,height:d,snazzyMapStyle:p,customPin:m}=o,[u,g]=(0,e.useState)(!1),[w,y]=(0,e.useState)(""),h=(0,l.useBlockProps)({className:s()({"waw-google-map-block":!0})}),f=window.wawGoogleMaps?.apiKey||"";(0,e.useEffect)(()=>{f?i&&window.google&&window.google.maps?b():i&&!window.google&&E():y("Google Maps API key not configured. Please add your API key in WAW Options > Google.")},[i,c,p,m,f]);const E=()=>{if(window.google&&window.google.maps)return void b();const e=document.createElement("script");e.src=`https://maps.googleapis.com/maps/api/js?key=${f}&libraries=places`,e.async=!0,e.defer=!0,e.onload=()=>{g(!0),b()},e.onerror=()=>{y("Failed to load Google Maps API")},document.head.appendChild(e)},b=()=>{if(!i||!window.google||!window.google.maps)return;const e=document.getElementById(`waw-map-editor-${a.clientId}`);e&&(new window.google.maps.Geocoder).geocode({address:i},(t,a)=>{if(console.log("Geocoding request for:",i),console.log("Geocoding status:",a),console.log("Geocoding results:",t),"OK"===a&&t[0]){const a={center:t[0].geometry.location,zoom:c,mapTypeId:window.google.maps.MapTypeId.ROADMAP};if(p)try{const e=JSON.parse(p);a.styles=e}catch(e){console.warn("Invalid Snazzy Maps style JSON")}const o=new window.google.maps.Map(e,a),n={position:t[0].geometry.location,map:o,title:i};m&&(n.icon={url:m,scaledSize:new window.google.maps.Size(40,40)}),new window.google.maps.Marker(n),y("")}else{let e="Geocoding failed: "+a;switch(a){case"ZERO_RESULTS":e='Address not found. Try a more specific address (e.g., "123 Main St, Sydney NSW, Australia")';break;case"OVER_QUERY_LIMIT":e="Too many requests. Please try again later.";break;case"REQUEST_DENIED":e="Geocoding request denied. Check your API key permissions.";break;case"INVALID_REQUEST":e="Invalid geocoding request. Please check the address format.";break;case"UNKNOWN_ERROR":e="Unknown error occurred. Please try again."}y(e)}})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Map Settings"),initialOpen:!0},(0,e.createElement)(n.PanelRow,null,(0,e.createElement)(n.TextControl,{label:"Address",value:i,onChange:e=>r({address:e}),placeholder:"Enter address or location",help:"Enter the address or location you want to display on the map"})),(0,e.createElement)(n.PanelRow,null,(0,e.createElement)(n.RangeControl,{label:"Zoom Level",value:c,onChange:e=>r({zoom:e}),min:1,max:20,help:"Adjust the zoom level of the map (1 = world view, 20 = street level)"})),(0,e.createElement)(n.PanelRow,null,(0,e.createElement)(n.RangeControl,{label:"Map Height (px)",value:d,onChange:e=>r({height:e}),min:200,max:800,help:"Set the height of the map in pixels"}))),(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Styling"),initialOpen:!1},(0,e.createElement)(n.PanelRow,null,(0,e.createElement)(n.TextareaControl,{label:"Snazzy Maps Style (JSON)",value:p,onChange:e=>r({snazzyMapStyle:e}),placeholder:'[{"featureType":"all","elementType":"labels.text.fill","stylers":[{"saturation":36}]}]',help:"Paste Snazzy Maps JSON style array to customize map appearance",rows:6})),(0,e.createElement)(n.PanelRow,null,(0,e.createElement)(l.MediaUploadCheck,null,(0,e.createElement)(l.MediaUpload,{value:m,onSelect:e=>{r({customPin:e.url})},type:"image",render:t=>(0,e.createElement)(n.Button,{onClick:t.open,className:"button"},m?"Change Custom Pin":"Select Custom Pin")}))),m&&(0,e.createElement)(n.PanelRow,null,(0,e.createElement)("div",null,(0,e.createElement)("img",{src:m,style:{maxWidth:"100px",height:"auto"},alt:"Custom pin preview"}),(0,e.createElement)("br",null),(0,e.createElement)(n.Button,{variant:"secondary",size:"small",onClick:()=>r({customPin:""})},(0,t.__)("Remove Custom Pin")))))),(0,e.createElement)("div",{...h},f?i?w?(0,e.createElement)(n.Notice,{status:"error",isDismissible:!1},w):(0,e.createElement)("div",{id:`waw-map-editor-${a.clientId}`,className:"waw-google-map-preview",style:{height:`${d}px`,width:"100%"}},"Loading map..."):(0,e.createElement)("div",{className:"waw-map-placeholder"},(0,e.createElement)("div",{className:"waw-map-placeholder-content"},(0,e.createElement)("span",{className:"dashicons dashicons-location-alt"}),(0,e.createElement)("p",null,"Enter an address in the block settings to display the map"))):(0,e.createElement)(n.Notice,{status:"error",isDismissible:!1},w)))},save:function(t){const{attributes:a}=t,{address:o,zoom:n,height:r,snazzyMapStyle:s,customPin:i}=a,c=l.useBlockProps.save({className:"waw-google-map-block"});return(0,e.createElement)("div",{...c},(0,e.createElement)("div",{className:"waw-google-map",style:{height:`${r}px`},"data-address":o,"data-zoom":n,"data-snazzy-style":s,"data-custom-pin":i}))}}})})()})();