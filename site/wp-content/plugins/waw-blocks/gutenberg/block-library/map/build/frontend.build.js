(()=>{"use strict";document.addEventListener("DOMContentLoaded",function(){const o=document.querySelectorAll(".waw-google-map");0!==o.length&&(void 0!==window.google&&void 0!==window.google.maps?o.forEach(function(o){!function(o){const e=o.getAttribute("data-address"),t=parseInt(o.getAttribute("data-zoom"))||15,a=o.getAttribute("data-snazzy-style"),n=o.getAttribute("data-custom-pin");e?(new window.google.maps.Geocoder).geocode({address:e},function(d,i){if("OK"===i&&d[0]){const i={center:d[0].geometry.location,zoom:t,mapTypeId:window.google.maps.MapTypeId.ROADMAP};if(a)try{const o=JSON.parse(a);i.styles=o}catch(o){console.warn("Invalid Snazzy Maps style JSON:",o)}const s=new window.google.maps.Map(o,i),r={position:d[0].geometry.location,map:s,title:e};n&&(r.icon={url:n,scaledSize:new window.google.maps.Size(40,40)}),new window.google.maps.Marker(r)}else o.innerHTML='<div class="waw-map-error">Failed to load map: '+i+"</div>"}):o.innerHTML='<div class="waw-map-error">No address specified</div>'}(o)}):console.warn("Google Maps API not loaded"))})})();