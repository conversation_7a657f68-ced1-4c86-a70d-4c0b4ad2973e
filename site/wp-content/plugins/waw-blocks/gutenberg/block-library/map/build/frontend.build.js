(()=>{"use strict";document.addEventListener("DOMContentLoaded",function(){const e=document.querySelectorAll(".waw-google-map");0!==e.length&&(void 0!==window.google&&void 0!==window.google.maps?e.forEach(function(e,o){e.id||(e.id=`waw-map-${o}-${Date.now()}`),function(e){const o=e.getAttribute("data-address"),a=parseInt(e.getAttribute("data-zoom"))||15,t=e.getAttribute("data-snazzy-style"),n=e.getAttribute("data-custom-pin");o?(new window.google.maps.Geocoder).geocode({address:o},function(s,d){if(console.log("Frontend geocoding request for:",o),console.log("Frontend geocoding status:",d),"OK"===d&&s[0]){const d={center:s[0].geometry.location,zoom:a,mapTypeId:window.google.maps.MapTypeId.ROADMAP};if(t)try{const e=JSON.parse(t);d.styles=e}catch(e){console.warn("Invalid Snazzy Maps style JSON:",e)}const r=new window.google.maps.Map(e,d),i={position:s[0].geometry.location,map:r,title:o};n&&(i.icon={url:n,scaledSize:new window.google.maps.Size(40,40)}),new window.google.maps.Marker(i)}else{let o="Failed to load map: "+d;switch(d){case"ZERO_RESULTS":o="Address not found. Please check the address and try again.";break;case"OVER_QUERY_LIMIT":o="Too many requests. Please try again later.";break;case"REQUEST_DENIED":o="Map request denied. Please contact the site administrator.";break;case"INVALID_REQUEST":o="Invalid map request.";break;case"UNKNOWN_ERROR":o="Unknown error occurred. Please try again."}e.innerHTML='<div class="waw-map-error">'+o+"</div>"}}):e.innerHTML='<div class="waw-map-error">No address specified</div>'}(e)}):console.warn("Google Maps API not loaded"))})})();