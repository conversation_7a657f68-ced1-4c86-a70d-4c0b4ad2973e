<?php

namespace WAWBlocks;

/**
 * Class Name: Block Class
 * Version: 1.0.1
 * Author: <PERSON><PERSON>
 * Created on: 2025-01-02
 * Last Modified: 2025-01-02
 * Description: This class is used to load Google Maps Gutenberg block JavaScript and CSS files,
 * initialize <PERSON><PERSON><PERSON>, and manage related assets for the block.
 * It includes methods for reading JSON configurations, registering and enqueuing scripts and styles,
 * and checking for Gutenberg's active status.
 * License: Commercial
 * Contact: <EMAIL>
 *
 * Dependencies:
 * - PHP Version: 7.4 or higher
 * - Google Maps API Key (stored in WAW Options)
 *
 * Change Log:
 * - 1.0.1 (2025-01-02): Updated class name and improved error handling.
 * - 1.0.0 (2025-01-02): Initial release.
 */
class GoogleMap
{
	private $block_name = '';
	private $block_dir = '';

	function __construct()
	{
		//--------------------------
		// LOAD JSON - START
		//--------------------------
		// Read the JSON file
		$json_data = $this->read_json();

		// Name - Used to register styles and scripts
		if (isset($json_data['name'])) {

			$this->block_name = $json_data['name'];
		}

		if (empty($this->block_name)) {
			return;
		}

		$modifiedString = str_replace('/', '-', $this->block_name);

		$this->block_name = $modifiedString;

		// Url - Used to register styles and scripts
		$this->block_dir = plugin_dir_url(__FILE__);
		//--------------------------
		// LOAD JSON - END
		//--------------------------


		//--------------------------
		// HOOKS - START
		//--------------------------
		add_action('init', [$this, 'register_block']);
		add_action('enqueue_block_editor_assets', [$this, 'editor_assets']);
		add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
		add_action('wp_footer', [$this, 'enqueue_google_maps_api']);
		//--------------------------
		// HOOKS - END
		//--------------------------
	}

	function read_json()
	{
		// Define the path to the JSON file
		$filePath = plugin_dir_path(__FILE__) . 'build/block.json';

		// $asset_file = include(plugin_dir_path(__FILE__) . 'build/index.build.asset.php');
		// Check if the file exists and is readable
		if (file_exists($filePath) && is_readable($filePath)) {

			// Read the JSON file
			$json = file_get_contents($filePath);

			// Decode the JSON file
			$json_data = json_decode($json, true);

			// Check for JSON decoding errors
			if (json_last_error() !== JSON_ERROR_NONE) {
				// Handle the error appropriately
				error_log("JSON decoding error: " . json_last_error_msg());
				// Send a user-friendly error message or take other appropriate actions
				return false;
			} else {
				return $json_data;
			}
		} else {
			// Handle the error if the file doesn't exist or isn't readable
			// error_losg("Error: Unable to access the JSON file: " . $this->block_name);
			// Send a user-friendly error message or take other appropriate actions
			return false;
		}
	}

	function editor_assets()
	{
		// Check if we're in the block editor
		if (!is_admin() || !wp_script_is('wp-block-editor', 'registered')) {
			return;
		}

		$asset_file = include(plugin_dir_path(__FILE__) . 'build/index.build.asset.php');
		$dependencies = isset($asset_file['dependencies']) ? $asset_file['dependencies'] : array();
		$version = isset($asset_file['version']) ? $asset_file['version'] : '1.0';

		wp_register_script(
			$this->block_name,
			$this->block_dir . 'build/index.build.js',
			$dependencies,
			$version
		);
		wp_enqueue_script($this->block_name);
		wp_enqueue_style($this->block_name . '-editor', $this->block_dir . 'build/index.css');

		// Localize script with Google Maps API key
		$api_key = get_option('google_maps_api_key', '');
		wp_localize_script($this->block_name, 'wawGoogleMaps', array(
			'apiKey' => $api_key,
			'isEditor' => true
		));
	}

	function register_block()
	{

		if (function_exists('is_gutenberg_active') && !$this->is_gutenberg_active()) {
			return;
		}

		if (is_admin()) {
			wp_enqueue_editor();
		}

		register_block_type(__DIR__ . '/build', array(
			'render_callback' => [$this, 'render_callback']
		));
	}

	function render_callback($attributes, $content)
	{
		$address = isset($attributes['address']) ? $attributes['address'] : '';
		$zoom = isset($attributes['zoom']) ? $attributes['zoom'] : 15;
		$height = isset($attributes['height']) ? $attributes['height'] : 400;
		$snazzyMapStyle = isset($attributes['snazzyMapStyle']) ? $attributes['snazzyMapStyle'] : '';
		$customPin = isset($attributes['customPin']) ? $attributes['customPin'] : '';
		$mapId = 'waw-map-' . uniqid();

		$api_key = get_option('google_maps_api_key', '');
		
		if (empty($api_key)) {
			return '<div class="waw-map-error">Google Maps API key not configured. Please add your API key in WAW Options > Google.</div>';
		}

		if (empty($address)) {
			return '<div class="waw-map-placeholder">Please enter an address in the block settings.</div>';
		}

		ob_start();
		?>
		<div class="waw-google-map" id="<?php echo esc_attr($mapId); ?>" style="height: <?php echo esc_attr($height); ?>px;" 
			 data-address="<?php echo esc_attr($address); ?>"
			 data-zoom="<?php echo esc_attr($zoom); ?>"
			 data-snazzy-style="<?php echo esc_attr($snazzyMapStyle); ?>"
			 data-custom-pin="<?php echo esc_attr($customPin); ?>">
		</div>
		<?php
		return ob_get_clean();
	}

	function enqueue_frontend_assets()
	{
		if (!is_admin()) { // Ensures the styles are not loaded in the admin area
			// Convert the URL to a file path
			$script_path = plugin_dir_path(__FILE__) . 'build/frontend.build.js';
			$style_path = plugin_dir_path(__FILE__) . 'build/frontend.css';

			// Check if the file exists
			if (file_exists($script_path)) {
				wp_enqueue_script(
					$this->block_name . '-frontend',
					$this->block_dir . 'build/frontend.build.js',
					array(),
					'1.0',
					true
				);

				// Localize script with Google Maps API key
				$api_key = get_option('google_maps_api_key', '');
				wp_localize_script($this->block_name . '-frontend', 'wawGoogleMaps', array(
					'apiKey' => $api_key,
					'isEditor' => false
				));
			}
			if (file_exists($style_path)) {
				// If the file exists, enqueue the style
				wp_enqueue_style(
					$this->block_name . '-frontend',
					$this->block_dir . 'build/frontend.css',
					array(),
					'1.0'
				);
			}
		}
	}

	function enqueue_google_maps_api()
	{
		$api_key = get_option('google_maps_api_key', '');
		if (!empty($api_key) && !is_admin()) {
			wp_enqueue_script(
				'google-maps-api',
				'https://maps.googleapis.com/maps/api/js?key=' . $api_key . '&libraries=places',
				array(),
				null,
				true
			);
		}
	}

	/**
	 * Check if Gutenberg is active
	 *
	 * @since 1.1.0
	 *
	 * @return boolean
	 */
	public function is_gutenberg_active()
	{
		return function_exists('register_block_type');
	}
}

new GoogleMap();
