(()=>{var n=window.gf_duplicate_submissions||{},a=function(){var i=window.navigator.userAgent,t=!!i.match(/iP(ad|od|hone)/i),n=!!i.match(/Safari/i),a=!i.match(/Chrome|CriOS|OPiOS|mercury|FxiOS|Firefox/i);return t?!!i.match(/WebKit/i)&&n&&a:void 0!==window.safari||n&&a},r=function(i,t,n){for(var n=n.split("#"),a=n[1]?"#"+n[1]:"",n=n[0].split("?"),r=n[0],n=n[1],o=void 0!==n?n.split("&"):[],e=!1,s=0;s<o.length;s++)o[s].startsWith(i+"=")&&(0<t.length?o[s]=i+"="+t:o.splice(s,1),e=!0);return!e&&0<t.length&&(o[o.length]=i+"="+t),r+"?"+o.join("&")+a},i=function(){var i=r(n.safari_redirect_param,"",window.location.href),t=r(n.safari_redirect_param,"1",window.location.href);return console.log(i,t),a()?t:i};!window.gf_duplicate_submissions_initialized&&"1"===n.is_gf_submission&&window.history.replaceState&&(window.gf_duplicate_submissions_initialized=!0,window.history.replaceState(null,null,i()))})();