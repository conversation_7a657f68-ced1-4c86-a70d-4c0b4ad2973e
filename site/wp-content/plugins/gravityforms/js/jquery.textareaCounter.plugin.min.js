(t=>{t.fn.textareaCount=function(i,r){var o,l=t(this),c=0,u=i.maxCharacterSize,s=0,f=0,h={};function d(r){for(var a=0,e=0;e<r.length;e++)"\n"===r.charAt(e)&&a++;return a}function p(){return-1!==navigator.appVersion.toLowerCase().indexOf("win")}function g(r){return(r+" ").replace(/^[^A-Za-z0-9]+/gi,"").replace(/[^A-Za-z0-9]+/gi," ").split(" ")}function m(r){return r.length-1}function a(){var r,a,e,t=l.val(),n=("function"==typeof i.charCounter?i.charCounter:h[i.charCounter])(t);return 0<i.maxCharacterSize?(r=d(t=i.truncate&&n>=i.maxCharacterSize?t.substring(0,i.maxCharacterSize):t),a=i.maxCharacterSize,p()&&(a=i.maxCharacterSize-r),i.truncate&&a<n&&(e=this.scrollTop,l.val(t.substring(0,a)),this.scrollTop=e),o.removeClass(i.warningStyle+" "+i.errorStyle),a-n<=i.warningNumber&&o.addClass(i.warningStyle),a-n<0&&o.addClass(i.errorStyle),c=n,p()&&(c=n+r),f=m(g(l.val())),s=u-c):(r=d(t),c=n,p()&&(c=n+r),f=m(g(l.val()))),e=(e=(e=i.displayFormat).replace("#input",c)).replace("#words",f),e=0<u?(e=e.replace("#max",u)).replace("#left",s):e}function e(){o.html(a()),void 0!==r&&r.call(this,{input:c,max:u,left:s,words:f})}h.standard=function(r){return r.length},h.twitter=function(r){var a=Array(23).join("*"),e=new RegExp("(https?://)?([a-z0-9+!*(),;?&=$_.-]+(:[a-z0-9+!*(),;?&=$_.-]+)?@)?([a-z0-9-.]*)\\.(travel|museum|[a-z]{2,4})(:[0-9]{2,5})?(/([a-z0-9+$_-]\\.?)+)*/?(\\?[a-z+&$_.-][a-z0-9;:@&%=+/$_.-]*)?(#[a-z_.-][a-z0-9+$_.-]*)?","gi");return r.replace(e,a).length},i=t.extend({maxCharacterSize:-1,truncate:!0,charCounter:"standard",originalStyle:"originalTextareaInfo",warningStyle:"warningTextareaInfo",errorStyle:"errorTextareaInfo",warningNumber:20,displayFormat:"#input characters | #words words"},i),t("<div class='charleft'>&nbsp;</div>").insertAfter(l),(o=l.next(".charleft")).addClass(i.originalStyle),e(),l.bind("keyup",function(){e()}).bind("mouseover paste",function(){setTimeout(function(){e()},10)})}})(jQuery);