var GFPageConditionalLogic=function(o){var r=this,a=jQuery;r.init=function(){r.options=o,r.triggerInputIds=r.getTriggerInputIds(r.options.pages),r.formWrapper="#gform_wrapper_"+r.options.formId,r.startAtZero=a(r.formWrapper+" .gf_progressbar_wrapper").data("startAtZero"),r.evaluatePages(),r.bindEvents()},r.bindEvents=function(){gform.addAction("gform_input_change",function(o,i,t){var e=parseInt(t,10)+"",t=-1!==a.inArray(t,r.triggerInputIds)||-1!==a.inArray(e,r.triggerInputIds);r.options.formId==i&&t&&r.evaluatePages()})},r.evaluatePages=function(){var i,t,e;for(let o=0;o<r.options.pages.length;o++)i=r.options.pages[o],t=r.evaluatePage(i,r.options.formId),e=r.isPageVisible(i),t||!1===e?t&&!e&&r.showPage(i):r.hidePage(i);gform.doAction("gform_frontend_pages_evaluated",r.options.pages,r.options.formId,r),gform.doAction("gform_frontend_pages_evaluated_{0}".gformFormat(r.options.formId),r.options.pages,r.options.formId,r),gform.utils.trigger({event:"gform/frontend_pages/evaluated",data:{formId:r.options.formId,pages:r.options.pages},native:!1})},r.evaluatePage=function(o,i){return!o.conditionalLogic||"show"===gf_get_field_action(i,o.conditionalLogic)},r.getTriggerInputIds=function(){for(var o=[],i=0;i<r.options.pages.length;i++){var t=r.options.pages[i];if(t.conditionalLogic)for(var e=0;e<t.conditionalLogic.rules.length;e++){var n=r.options.pages[i].conditionalLogic.rules[e];-1===a.inArray(n.fieldId,o)&&o.push(n.fieldId)}}return o},r.isPageVisible=function(o){return!("object"!=typeof o&&!(o=r.getPage(o)))&&(void 0!==o.isVisible?o.isVisible:null)},r.getPage=function(o){for(var i=0;i<r.options.pages.length;i++){var t=r.options.pages[i];if(t.fieldId==o)return t}return!1},r.showPage=function(o){!0!==r.isPageVisible(o)&&(o.isVisible=!0,a("#gform_"+r.options.formId+' div[data-js="page-field-id-'+o.fieldId+'"]').attr("data-conditional-logic","visible"),gform.doAction("gform_frontend_page_visible",o,r.options.formId),gform.doAction("gform_frontend_page_visible_{0}".gformFormat(r.options.formId),o,r.options.formId))},r.hidePage=function(o){!1!==r.isPageVisible(o)&&(o.isVisible=!1,a("#gform_"+r.options.formId+' div[data-js="page-field-id-'+o.fieldId+'"]').attr("data-conditional-logic","hidden"),gform.doAction("gform_frontend_page_hidden",o,r.options.formId),gform.doAction("gform_frontend_page_hidden_{0}".gformFormat(r.options.formId),o,r.options.formId))},this.init()};