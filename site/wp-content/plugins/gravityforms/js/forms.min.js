function Form(){this.id=0,this.title=gf_vars.formTitle,this.description=gf_vars.formDescription,this.labelPlacement="top_label",this.subLabelPlacement="below",this.maxEntriesMessage="",this.confirmation=new Confirmation,this.button=new Button,this.fields=new Array}function Confirmation(){this.type="message",this.message=gf_vars.formConfirmationMessage,this.url="",this.pageId="",this.queryString=""}function Button(){this.type="text",this.text=gf_vars.buttonText,this.imageUrl=""}function Field(i,t){this.id=i,this.formId=window.form.id,this.label="",this.adminLabel="",this.type=t,this.isRequired=!1,this.size="large",this.errorMessage="",this.visibility="visible"}function Choice(i,t,s){this.text=i,this.value=t||i,this.isSelected=!1,this.price=s||""}function Input(i,t,s){this.id=i,this.label=t,this.name="",void 0!==s&&(this.autocompleteAttribute=s)}function ConditionalLogic(){this.actionType="show",this.logicType="all",this.rules=[new ConditionalRule]}function ConditionalRule(){this.fieldId=0,this.operator="is",this.value=""}