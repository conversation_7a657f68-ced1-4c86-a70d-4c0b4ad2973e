function initLayoutEditor(p){p.fn.setGroupId=function(e){return this.attr("data-groupId",e),this.each(function(){var t=W(p(this));t&&(t.layoutGroupId=e)}),this},p.fn.setGridColumnSpan=function(e){var i;return null===e?this.css("grid-column","auto / auto"):(this.css("grid-column","span {0}".gformFormat(e)),this.each(function(){var t;p(this).hasClass("spacer")?(t=p(this).prev(".gfield"),(i=W(t)).layoutSpacerGridColumnSpan=e):(i=W(p(this)))&&(i.layoutGridColumnSpan=e)})),this},p.fn.getGridColumnSpan=function(){var t;if(void 0!==this.css("gridColumnStart"))return t=parseInt(this.css("gridColumnStart").split(" ")[1]),isNaN(t)&&void 0!==y?y:t},p.fn.resizeGroup=function(t){j(t)},String.prototype.gformFormat||(String.prototype.gformFormat=function(){var i=arguments;return this.replace(/{(\d+)}/g,function(t,e){return void 0!==i[e]?i[e]:t})});var o,a,t,c=p("#form_editor_fields_container"),m=p(".gform_editor"),_=p("#gform_fields"),e=p("#no-fields"),r=p("#no-fields-drop"),v=p(".editor-sidebar"),C=p(".gfield-field-action"),n=M(),b=null,i=".add-buttons button",y=getComputedStyle(_[0])["grid-template-columns"].split(" ").length,x=y/4,u=null,d=!1;function l(t,e){var i='<li data-js-field-loading-placeholder><div class="dropzone__loader"><div class="dropzone__loader-item dropzone__loader-label"></div><div class="dropzone__loader-item dropzone__loader-content"></div></div></li>';void 0!==e?0===e?p("#gform_fields").prepend(i):p("#gform_fields").children().eq(e-1).after(i):jQuery("#field_submit")?jQuery(i).insertBefore(jQuery("#field_submit")):p("#gform_fields").append(i),p("[data-js-field-loading-placeholder]").setGridColumnSpan(y),p("#form_editor_fields_container").addClass("dropzone-loader-visible"),h(p("[data-js-field-loading-placeholder]"),k(!1).data("target"),k(!1).data("where"))}function s(){p("#form_editor_fields_container").removeClass("dropzone-loader-visible"),p("[data-js-field-loading-placeholder]").remove()}function f(i){i.hasClass("ui-draggable")&&i.draggable("destroy").resizable("destroy"),i.draggable({helper:"clone",zIndex:999,handle:".gfield-drag",create:function(t,e){var i,o;Q(p(this))||((o=!!(o=p(this).attr("id").replace("field_",""))&&GetFieldById(o))&&o.layoutGroupId&&!m.hasClass("gform_legacy_markup")?i=o.layoutGroupId:G(p(this),!1)||(i=G()),p(this).setGroupId(i))},start:function(t,e){_.addClass("dragging"),c.addClass("droppable"),(b=p(this)).addClass("placeholder")},drag:function(t,e){e.helper.width(b.width()).height(b.height()).setGridColumnSpan(null),helperLeft=gform.tools.isRtl()?e.position.left+e.helper.outerWidth():e.position.left,g(0,e,e.position.top,helperLeft)},stop:function(t,e){_.removeClass("dragging"),c.removeClass("droppable"),b.removeClass("placeholder"),M().removeClass("hovering"),k().data("target")&&h(b,k().data("target"),k().data("where")),k().remove(),e.helper.remove()}}).resizable({handles:"e, w",start:function(t,e){"1"===gf_legacy.is_legacy?(i.resizable("option","minWidth",e.size.width),i.resizable("option","maxWidth",e.size.width),alert(gf_vars.alertLegacyMode)):(u=null,_.addClass("resizing"))},resize:function(t,e){var i,o,r,a,n,d,l,s,f;"1"!==gf_legacy.is_legacy&&(f=_.outerWidth()/y,o=(i=e.element).outerWidth(),o=Math.max(x,Math.round(o/f)),f=i.getGridColumnSpan(),r=S(G(i)),d=i,l=1===(l=(l=r).not(".spacer")).length||l.last()[0]===d[0],d=r.filter(".spacer"),a=l&&!d.length?null:i.next(),null===u&&(u=1<r.length?f+(n=a?w(a):0):y),x="gform_editor_submit_container"===e.element.data("fieldClass")?1:y/4,f=u,"gform_editor_submit_container"===i.next().data("fieldClass")?f=u-1:1<r.length&&!l&&(f=u-x),s=x,f=f,o=Math.max(s,Math.min(f,o)),p().add(e.helper).add(e.element).css("width","auto").css("left","auto").setGridColumnSpan(o),a&&(n=u-o,a.css("width","auto").setGridColumnSpan(n)),o==y||o==u?A(d):l&&!d.length&&w(r)<y&&(f=G(s=i),e=1,f=p('<div class="spacer gfield"></div>').setGroupId(f).setGridColumnSpan(e),s.after(f)))},stop:function(){"1"!==gf_legacy.is_legacy&&_.removeClass("resizing")}})}function g(t,d,l,s){M().removeClass("hovering");var e,i,o,r,a,n,f,u,g,h=0<p(".gform-compact-view").length;e=s,i=l,g=h,a=(gform.tools.isRtl()?_:c).offset().left,o=_.offset(),r=o.top-c.offset().top,o=o.left-a,a=C.outerWidth()||null,g=g?-r:-r+a,a=-o+c.outerWidth()-v.outerWidth()-a,r=-r+c.outerHeight(),o=-o,g<i&&i<r&&o<e&&e<a?(g=u=h?(n=-9,f=9,5):(n=-10,f=10,0),l<0?k().css({top:n,left:0,height:"4px",width:_.outerWidth()}).data({where:"top",target:M().first()}):l>_.outerHeight()?"gform_editor_submit_container"!==M().last().data("field-class")&&"gform_editor_submit_container"!==M().last().prev().data("field-class")&&k().css({top:_.outerHeight()-g,left:0,height:"4px",width:_.outerWidth()}).data({where:"bottom",target:M().last()}):M().not(d.helper).not(this).each(function(){var t=p(this),e=t.position(),i={top:e.top,right:e.left+t.outerWidth(),bottom:e.top+t.outerHeight(),left:e.left};if(a=s,(r=l)<(n=i).bottom&&r>n.top&&a<n.right&&a>n.left){t.addClass("hovering"),Q(t)&&(e=(t=t.prev()).position(),o="right");var o=((t,e,i,o,r)=>{var a=i.left+o/2,o=i.right-o/2,n=i.top+r/5,r=i.bottom-r/5;return i.top<e&&e<n?"top":e<i.bottom&&r<e?"bottom":t>i.left&&t<a?"left":t<i.right&&o<t?"right":"center"})(s,l,i,t.outerWidth(),t.outerHeight()),r=S(G(t),!1),a=r.length>=y/x,n=(G(t)===G(d.helper)&&(a=!1),((t,e)=>{var i,o,r;return o=G(e),t=G(t.helper),i=S(o),o===t||(Q(e)?e=(r=e).prev():Q(e.next())&&!1!==i.index(e.next())&&(r=e.next()),o=(r?r.getGridColumnSpan():null)||(I(i)?y/(i.length+1):e.getGridColumnSpan()/2),!(parseInt(o)<3)&&void 0)})(d,t));if("gform_editor_submit_container"===t.data("field-class")){if(gform.tools.isRtl()&&("left"===o||"bottom"===o))return;if("right"===o||"bottom"===o)return}if("left"===o||"right"===o){if("bottom"===t.data("field-position"))return;if(!((t,e)=>!m.hasClass("gform_legacy_markup")&&!(t.hasClass("gpage")||t.hasClass("gsection")||t.hasClass("gform_hidden")||e.hasClass("gpage")||e.hasClass("gsection")||e.hasClass("gform_hidden")||"hidden"===e.data("type")||e.is("button")&&-1!==p.inArray(e.val().toLowerCase(),["page","section"])))(t,b))return;if(a||!1===n)return}if(!("bottom"===o&&0<r.filter('[data-field-class="gform_editor_submit_container"]').length))switch(k().data({where:o,target:t}),o){case"left":return k().css({top:e.top,left:e.left-10,height:t.outerHeight(),width:"4px"}),!1;case"right":return k().css({top:e.top,left:e.left+t.outerWidth()+6,right:"auto",height:t.outerHeight(),width:"4px"}),!1;case"bottom":return k().css({top:e.top+t.outerHeight()+u,left:0,height:"4px",width:"100%"}),!1;case"top":return k().css({top:e.top-f,left:0,height:"4px",width:"100%"}),!1}}})):k(!1).remove()}function h(t,e,i){var o,r,a,n,d,l,s,f;e&&!e.hasClass("gform_button")&&(d=G(t),s=S(l=G(e)),Q(e)?e=(f=e).prev():(Q(e.next())||0<e.next().filter("[data-js-field-loading-placeholder]").length)&&!1!==s.index(e.next())&&(f=e.next()),n="left"===i||"right"===i,f&&n&&(o=f.getGridColumnSpan(),A(f),s=S(l)),"top"==i?e=s.first():"bottom"==i&&(e=s.last()),f=gform.tools.isRtl()?"right":"left","top"==i||i==f?t.insertBefore(e):t.insertAfter(e),n?(o?(a=t,r=o):a=(I(s)?(r=y/(s.length+1),s):(r=(o=e.getGridColumnSpan())/2,e)).add(t),parseInt(r)==r?a.setGridColumnSpan(r):(i=Math.floor(r),f=Math.ceil(r),t.setGridColumnSpan(i),e.setGridColumnSpan(f))):(l=G(),t.setGridColumnSpan(y)),t.setGroupId(l),j(d))}function G(t,e){var i;return i=(i=void 0!==t?t.attr("data-groupId"):i)||!e&&void 0!==e?i:"xxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})}function S(t,e){return e||void 0===e?M().filter('[data-groupId="{0}"]'.gformFormat(t)).not(".ui-draggable-dragging"):M().filter('[data-groupId="{0}"]'.gformFormat(t)).not(".ui-draggable-dragging").not(".spacer")}function w(t){var e=0;return t.each(function(){e+=p(this).getGridColumnSpan()}),e}function I(t){var e,i;return 0===t.length?i=!0:(e=t.first().getGridColumnSpan(),i=!0,t.each(function(){if(p(this).getGridColumnSpan()!==e)return i=!1}),i)}function j(t){var t=S(t),e=y/t.length,i=t.filter(".spacer");t[0]===i[0]&&0<t.length&&A(i),t.setGridColumnSpan(e)}function z(){SetSubmitLocation("bottom"),jQuery("#field_submit").attr("data-field-position","bottom"),jQuery('input[name="submit_location"][value="bottom"]').prop("checked",!0)}function A(t){t.setGridColumnSpan(0).remove()}function Q(t){return 0<t.filter(".spacer").length}function W(t){t=t.attr("id"),t=!(!t||-1===t.indexOf("field_"))&&String(t).replace("field_","");return!!t&&GetFieldById(t)}function F(t){return StartAddField(t,Math.max(0,_.children().index(b)))}function H(){M().removeClass("field_selected"),p(".sidebar").tabs("option","active",0),HideSettings()}function M(){return _.find(".gfield")}function k(t){t=void 0===t;var e=p("#indicator");return!e.length&&t&&(e=p('<div id="indicator"></div>'),_.append(e)),e}f(n),"1"!==window.gf_legacy.is_legacy&&n.length&&(t=(()=>{var t=[],e=[],i=n[0].offsetTop;return n.each(function(){i!==this.offsetTop&&e.length&&(t.push(e),e=[]),e.push({el:this,groupId:this.dataset.groupid}),i=this.offsetTop}),t})(),a=[],t.forEach(function(t){var e,i,o=[],r=!1;t.forEach(function(t){-1!==a.indexOf(t.groupId)&&(r=!0),o.push(t.groupId)}),o.every(function(t,e,i){return t===i[0]})&&!r||(e=t,i=G(),e.forEach(function(t){p(t.el).setGroupId(i)})),a.push(t[0].groupId)})),"inline"===p("#field_submit").attr("data-field-position")&&(t=jQuery("#field_submit").prev().attr("data-groupid"),jQuery("#field_submit").setGroupId(t)),p(i).on("mousedown touchstart",function(){gform.tools.trigger("gform/flyout/close-all"),p(this).attr("title","")}).draggable({helper:"clone",revert:function(){return!1},cancel:!1,appendTo:_,containment:"document",start:function(t,e){if(H(),c.addClass("droppable"),1==gf_vars.currentlyAddingField)return!1;e.helper.addClass("gform-theme__disable"),e.helper.width(p(this).width()).height(p(this).height()),_.addClass("dragging"),(b=p(this).clone()).addClass("placeholder"),p(this).addClass("fieldPlaceholder")},drag:function(t,e){var i,o;form.fields.length&&(i=+e.position.top+e.helper.outerHeight()/2,o=+e.position.left+e.helper.outerWidth()/2,g(0,e,i,o))},stop:function(t,e){p(this).removeClass("fieldPlaceholder"),c.removeClass("droppable"),_.removeClass("dragging"),e.helper.removeClass("gform-theme__disable");var i=!1;!form.fields.length&&d?(d=!1,i=F(e.helper.data("type"))):form.fields.length&&k(!1).data("target")&&(i=F(e.helper.data("type"))),i||(k(!1).remove(),b.remove(),b=null),p(this).attr("title",p(this).attr("data-description"))}}).on("click keypress",function(){b=null}),e.droppable({accept:i,activate:function(t,e){r.show(),p(this).addClass("ready")},over:function(){p(this).addClass("hovering"),r.addClass("hovering")},out:function(){p(this).removeClass("hovering"),r.removeClass("hovering")},drop:function(){d=!0,p(this).removeClass("hovering"),r.removeClass("hovering")},deactivate:function(){p(this).removeClass("ready")}}),c.on("click",function(){H()}),p(document).on("gform_field_added",function(t,e,i){var o=p("#field_"+i.id),o=(null===b?(o.setGroupId(G()),"inline"==jQuery("#field_submit").attr("data-field-position")&&z()):(h(o,k().data("target"),k().data("where")),b.remove(),b=null),c.hasClass("form_editor_fields_no_fields")&&(gform.simplebar.initializeInstance(c[0]),setTimeout(function(){r.hide(),c.removeClass("form_editor_fields_no_fields")},200)),k().remove(),f(o),"page"===i.type&&(z(),jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!0),SetFieldAccessibilityWarning("submit_location_setting","below")),0<!jQuery("#field_submit").length&&StartAddField("submit",Math.max(0,_.children().index(b)+1)),new Event("gform/layout_editor/field_modified"));document.dispatchEvent(o)}),p(document).on("gform_field_deleted",function(t,e,i){o=G(p("#field_"+i)),HasPageField()||(jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!1),jQuery(".submit_location_setting").prev(".gform-alert--notice").remove());i=new Event("gform/layout_editor/gform_field_deleted");document.dispatchEvent(i)}),gform.addAction("gform_after_field_removed",function(t,e){j(o)}),gform.addAction("gform_field_duplicated",function(t,e,i,o){S(G(p("#field_"+o))).last().after(i),i.setGridColumnSpan(y).setGroupId(G()),f(i)}),gform.addAction("gform_after_refresh_field_preview",function(t){f(p("#field_"+t))}),gform.addAction("gform_after_change_input_type",function(t){f(p("#field_"+t))}),gform.addAction("gform_before_get_field_markup",function(t,e,i){l(0,i)}),gform.addAction("gform_after_get_field_markup",function(t,e,i){s()}),gform.addAction("gform_after_get_field_markup",function(t,e,i){f(jQuery("#field_submit"))}),gform.addAction("gform_before_field_duplicated",function(t){t=p("#field_"+t);l(0,_.children().index(t)+1)}),gform.addAction("gform_field_duplicated",function(){s()}),gform.addAction("gform_before_refresh_field_preview",function(t){jQuery("#field_"+t).addClass("loading")}),gform.addAction("gform_after_refresh_field_preview",function(t){jQuery("#field_"+t).removeClass("loading")})}initLayoutEditor(jQuery);