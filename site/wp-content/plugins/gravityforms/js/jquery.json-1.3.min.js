!function($){function toIntegersAtLease(e){return e<10?"0"+e:e}Date.prototype.toJSON=function(e){return this.getUTCFullYear()+"-"+toIntegersAtLease(this.getUTCMonth())+"-"+toIntegersAtLease(this.getUTCDate())};var escapeable=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};$.quoteString=function(e){return'"'+e.replace(escapeable,function(e){var t=meta[e];return"string"==typeof t?t:(t=e.charCodeAt(),"\\u00"+Math.floor(t/16).toString(16)+(t%16).toString(16))})+'"'},$.toJSON=function(e,t){var r=typeof e;if("undefined"==r)return"undefined";if("number"==r||"boolean"==r)return e+"";if(null===e)return"null";if("string"==r)return $.quoteString(e);if("object"==r&&"function"==typeof e.toJSON)return e.toJSON(t);if("function"!=r&&"number"==typeof e.length){for(var n=[],o=0;o<e.length;o++)n.push($.toJSON(e[o],t));return t?"["+n.join(",")+"]":"["+n.join(", ")+"]"}if("function"==r)throw new TypeError("Unable to convert object of type 'function' to json.");var i,f,n=[];for(i in e){if("number"==(r=typeof i))f='"'+i+'"';else{if("string"!=r)continue;f=$.quoteString(i)}var u=$.toJSON(e[i],t);"string"==typeof u&&(t?n.push(f+":"+u):n.push(f+": "+u))}return"{"+n.join(", ")+"}"},$.compactJSON=function(e){return $.toJSON(e,!0)},$.evalJSON=function(src){return eval("("+src+")")},$.secureEvalJSON=function(src){var filtered=src,filtered=filtered.replace(/\\["\\\/bfnrtu]/g,"@");if(filtered=filtered.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]"),filtered=filtered.replace(/(?:^|:|,)(?:\s*\[)+/g,""),/^[\],:{}\s]*$/.test(filtered))return eval("("+src+")");throw new SyntaxError("Error parsing JSON, source is not valid.")}}(jQuery);