((c,p,t)=>{function y(){var e=t.datepicker;return{dayNamesMin:[e.days.sunday,e.days.monday,e.days.tuesday,e.days.wednesday,e.days.thursday,e.days.friday,e.days.saturday],monthNamesShort:[e.months.january,e.months.february,e.months.march,e.months.april,e.months.may,e.months.june,e.months.july,e.months.august,e.months.september,e.months.october,e.months.november,e.months.december],firstDay:e.firstDay,iconText:e.iconText}}function a(e){var t,a,s,r,o,i,d,m=y(),n=e.attr("id")?e.attr("id"):"",h=(t=e,h=y(),a=0<t.closest(".gform_wrapper").length,s=0<c("#preview_form_container").length,r="rtl"===window.getComputedStyle(t[0],null).getPropertyValue("direction"),o=a?t.closest(".gform_wrapper").data("form-theme"):"gravity-theme",i=a?t.closest(".gform_wrapper").attr("id").replace("gform_wrapper_",""):"",d=a?t.closest(".gform_wrapper").attr("data-form-index"):"",{yearRange:"-100:+20",showOn:"focus",dateFormat:"mm/dd/yy",dayNamesMin:h.dayNamesMin,monthNamesShort:h.monthNamesShort,firstDay:h.firstDay,changeMonth:!0,changeYear:!0,isRTL:r,showOtherMonths:a,suppressDatePicker:!1,onClose:function(){var e=this;t.focus(),this.suppressDatePicker=!0,setTimeout(function(){e.suppressDatePicker=!1},200)},beforeShow:function(e,t){return t.dpDiv[0].classList.remove("gform-theme-datepicker"),t.dpDiv[0].classList.remove("gravity-theme"),t.dpDiv[0].classList.remove("gform-theme"),t.dpDiv[0].classList.remove("gform-legacy-datepicker"),t.dpDiv[0].classList.remove("gform-theme--framework"),t.dpDiv[0].classList.remove("gform-theme--foundation"),t.dpDiv[0].classList.remove("gform-theme--orbital"),a&&(t.dpDiv[0].classList.add("gform-theme-datepicker"),c(t.dpDiv[0]).attr("data-parent-form",i+"_"+d)),void 0===o||"gravity-theme"===o?c(t.dpDiv[0]).addClass("gravity-theme"):"legacy"===o?c(t.dpDiv[0]).addClass("gform-legacy-datepicker"):(c(t.dpDiv[0]).addClass("gform-theme--"+o),"orbital"===o&&(c(t.dpDiv[0]).addClass("gform-theme--framework"),c(t.dpDiv[0]).addClass("gform-theme--foundation"))),r&&s&&(e=c(e).closest(".gfield"),e=c(document).outerWidth()-(e.offset().left+e.outerWidth()),t.dpDiv[0].style.right=e+"px"),!this.suppressDatePicker}});e.hasClass("dmy")?h.dateFormat="dd/mm/yy":e.hasClass("dmy_dash")?h.dateFormat="dd-mm-yy":e.hasClass("dmy_dot")?h.dateFormat="dd.mm.yy":e.hasClass("ymd_slash")?h.dateFormat="yy/mm/dd":e.hasClass("ymd_dash")?h.dateFormat="yy-mm-dd":e.hasClass("ymd_dot")&&(h.dateFormat="yy.mm.dd"),e.hasClass("gdatepicker_with_icon")?(h.showOn="both",h.buttonImage=e.parent().siblings("[id^='gforms_calendar_icon_input']").val(),h.buttonImageOnly=!0,h.buttonText=m.iconText):h.showOn="focus",n=n.split("_"),h=p.applyFilters("gform_datepicker_options_pre_init",h,n[1],n[2],e),e.datepicker(h),e.is(":input")&&e.click(function(){e.datepicker("show")})}function e(){c(".gform-datepicker:not(.initialized)").each(function(){var e=c(this);a(e),e.addClass("initialized")})}c(document).ready(e),window.gformInitDatepicker=e,window.gformInitSingleDatepicker=a})(jQuery,gform,gform_i18n);