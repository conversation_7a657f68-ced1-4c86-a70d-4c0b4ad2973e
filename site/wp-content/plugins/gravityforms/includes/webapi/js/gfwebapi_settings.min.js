function gfapiCalculateSig(e,r){e=CryptoJS.HmacSHA1(e,r).toString(CryptoJS.enc.Base64);return encodeURIComponent(e)}jQuery(document).ready(function(){jQuery("#gfwebapi-qrbutton").click(function(){jQuery("#gfwebapi-qrcode-container").toggle();var e=jQuery("#gfwebapi-qrcode");return 0<e.length&&e.attr("src",ajaxurl+"?action=gfwebapi_qrcode&rnd="+Date.now()),!1}),jQuery("#public_key, #private_key").on("keyup",function(){jQuery("#gfwebapi-qrcode-container").html("The keys have changes. Please save the changes and try again.")}),jQuery("#gfapi-url-builder-button").click(function(e){e.preventDefault(),e=jQuery("#public_key").val(),r=jQuery("#private_key").val(),t=parseInt(jQuery("#gfapi-url-builder-expiration").val()),o=jQuery("#gfapi-url-builder-method").val(),a=(a=jQuery("#gfapi-url-builder-route").val()).replace(/\/$/,"");var r,a,i=new Date,i=parseInt(i.getTime()/1e3)+t,t=gfapiCalculateSig(e+":"+o+":"+a+":"+i,r),o=gfapiBaseUrl+"/"+a+"/?api_key="+e+"&signature="+t+"&expires="+i;return jQuery("#gfapi-url-builder-generated-url").val(o),!1}),jQuery("#gfapi-url-tester-button").click(function(e){var i=jQuery(this),t=jQuery("#gfapi-url-tester-loading"),o=jQuery("#gfapi-url-tester-results"),r=jQuery("#gfapi-url-tester-url").val(),a=jQuery("#gfapi-url-tester-method").val();return jQuery.ajax({url:r+"&test=1",type:a,dataType:"json",data:{},beforeSend:function(e,r){i.attr("disabled","disabled"),t.show()}}).done(function(e,r,a){i.removeAttr("disabled"),t.hide(),o.html(a.status),o.fadeTo("fast",1)}).fail(function(e){var r;i.removeAttr("disabled"),t.hide(),o.fadeTo("fast",1),t.hide(),r="abort"==r?"Request cancelled":e.status+": "+e.statusText,o.html(r)}),!1}),jQuery("body").on("thickbox:removed",function(e){modalSubmitted&&jQuery("#gform-settings").submit()})});var modalSubmitted=!1;function saveKey(){var e={action:"gfwebapi_edit_key",nonce:jQuery('#gform-webapi-edit input[name="_wpnonce"]').val(),key_id:jQuery("#gform-webapi-key").val(),description:jQuery("#gform-webapi-description").val(),user_id:jQuery("#gform-webapi-user").val(),permissions:jQuery("#gform-webapi-permissions").val()};return jQuery.ajax({url:ajaxurl,type:"POST",dataType:"json",data:e,success:function(e){var r=e.success?"success":"error";jQuery("#gform-webapi-edit .alert",document).remove(),jQuery("#gform-webapi-edit").prepend('<div class="alert '+r+'">'+e.data.message+"</div>"),(e.data.key?(jQuery("#gform-webapi-description, #gform-webapi-user, #gform-webapi-permissions, #gform-webapi-truncated-key, #gform-webapi-last-access").parent().hide(),jQuery("#gform-webapi-consumer-key").val(e.data.key.consumer_key).parent().show(),jQuery("#gform-webapi-consumer-secret").val(e.data.key.consumer_secret).parent().show(),jQuery("#gform-webapi-edit button")):(jQuery("#gform-webapi-consumer-key").val("").parent().hide(),jQuery("#gform-webapi-consumer-secret").val("").parent())).hide()}}),!(modalSubmitted=!0)}function editKey(e){modalSubmitted=!1,jQuery("#gform-webapi-edit .alert",document).remove(),jQuery("#gform-webapi-consumer-key, #gform-webapi-consumer-secret").parent().hide(),jQuery("#gform-webapi-edit button").show(),0==e?(jQuery("#gform-webapi-key").val(e),jQuery("#gform-webapi-description").val(""),jQuery("#gform-webapi-user").val(jQuery("#gform-webapi-user option:first-child").val()),jQuery("#gform-webapi-permissions").val(jQuery("#gform-webapi-permissions option:first-child").val()),jQuery("#gform-webapi-edit button").html(jQuery("#gform-webapi-edit button").data("add")),jQuery("#gform-webapi-key, #gform-webapi-description, #gform-webapi-user, #gform-webapi-permissions").parent().show(),jQuery("#gform-webapi-truncated-key, #gform-webapi-last-access").parent().hide(),tb_show("Add New Key","#TB_inline?width=375&height=330&inlineId=gform-webapi-edit-container"),jQuery("#gform-webapi-edit",document).on("submit",saveKey)):jQuery.ajax({url:ajaxurl,type:"GET",dataType:"json",data:{action:"gfwebapi_edit_key",key_id:e,nonce:jQuery('#gform-webapi-edit input[name="_wpnonce"]').val()},success:function(e){var r;e.success?(r=e.data.key,jQuery("#gform-webapi-key").val(r.key_id),jQuery("#gform-webapi-description").val(r.description),jQuery("#gform-webapi-user").val(r.user_id),jQuery("#gform-webapi-permissions").val(r.permissions),jQuery("#gform-webapi-truncated-key").html(r.consumer_key).parent().show(),jQuery("#gform-webapi-last-access").html(r.last_access).parent().show(),jQuery("#gform-webapi-edit button").html(jQuery("#gform-webapi-edit button").data("edit")),jQuery("#gform-webapi-description, #gform-webapi-user, #gform-webapi-permissions, #gform-webapi-truncated-key, #gform-webapi-last-access").parent().show(),tb_show("Edit Key","#TB_inline?width=375&height=445&inlineId=gform-webapi-edit-container"),jQuery("#gform-webapi-edit",document).on("submit",saveKey)):alert(e.data.message)}})}