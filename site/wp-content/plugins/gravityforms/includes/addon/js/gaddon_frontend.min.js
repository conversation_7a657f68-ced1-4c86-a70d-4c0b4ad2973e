var GFFrontendFeeds=function(o){var r=this,f=jQuery;r.init=function(){r.options=o,r.triggerInputIds=r.getTriggerInputIds(r.options.feeds),r.activeFeeds=[],r.evaluateFeeds(),r.bindEvents()},r.bindEvents=function(){gform.addAction("gform_input_change",function(o,e,t){var d=parseInt(t)+"",t=-1!==f.inArray(t,r.triggerInputIds)||-1!==f.inArray(d,r.triggerInputIds);r.options.formId==e&&t&&r.evaluateFeeds()})},r.saveToState=function(){gform.state.set(r.options.formId,"feeds",r.options.feeds)},r.evaluateFeeds=function(){var o,e,t;for(i=0;i<r.options.feeds.length;i++)o=r.options.feeds[i],e=r.evaluateFeed(o,r.options.formId),t=r.isFeedActivated(o),e||null===t?!e||t||o.isSingleFeed&&(o.isSingleFeed,!r.hasPriority(o.feedId,o.addonSlug))||r.activateFeed(o):r.deactivateFeed(o);gform.doAction("gform_frontend_feeds_evaluated",r.options.feeds,r.options.formId,r),gform.doAction("gform_frontend_feeds_evaluated_{0}".gformFormat(r.options.formId),r.options.feeds,r.options.formId,r),gform.doAction("gform_{0}_frontend_feeds_evaluated".gformFormat(o.addonSlug),r.options.feeds,r.options.formId,r),gform.doAction("gform_{0}_frontend_feeds_evaluated_{0}".gformFormat(o.addonSlug,r.options.formId),r.options.feeds,r.options.formId,r),r.saveToState()},r.evaluateFeed=function(o,e){return!o.conditionalLogic||"show"==gf_get_field_action(e,o.conditionalLogic)},r.getTriggerInputIds=function(){for(var o=[],e=0;e<r.options.feeds.length;e++){var t=r.options.feeds[e];if(t.conditionalLogic)for(var d=0;d<t.conditionalLogic.rules.length;d++){var n=r.options.feeds[e].conditionalLogic.rules[d];-1==f.inArray(n.fieldId,o)&&o.push(n.fieldId)}}return o},r.isFeedActivated=function(o){return!("object"!=typeof o&&!(o=r.getFeed(o)))&&(void 0!==o.isActivated?o.isActivated:null)},r.getFeed=function(o){for(var e=0;e<r.options.feeds.length;e++){var t=r.options.feeds[e];if(t.feedId==o)return t}return!1},r.getFeedsByAddon=function(o,e,t){for(var d=[],n=0;n<r.options.feeds.length;n++){var i=r.options.feeds[n];i.addonSlug!=o||e&&i.feedId==e.feedId||t&&!r.isFeedActivated(i)||d.push(i)}return d},r.activateFeed=function(o){o.feedId&&(o=[o]);for(var e=0;e<o.length;e++){var t=o[e];t.isActivated=!0,gform.doAction("gform_frontend_feed_activated",t,r.options.formId),gform.doAction("gform_frontend_feed_activated_{0}".gformFormat(r.options.formId),t,r.options.formId),gform.doAction("gform_{0}_frontend_feed_activated".gformFormat(t.addonSlug),t,r.options.formId),gform.doAction("gform_{0}_frontend_feed_activated_{0}".gformFormat(t.addonSlug,r.options.formId),t,r.options.formId),t.isSingleFeed&&r.deactivateFeed(r.getFeedsByAddon(t.addonSlug,t))}},r.deactivateFeed=function(o){o.feedId&&(o=[o]);for(var e=0;e<o.length;e++){var t=o[e],d=r.isFeedActivated(t);null!==d&&!1!==d&&(t.isActivated=!1,gform.doAction("gform_frontend_feed_deactivated",t,r.options.formId),gform.doAction("gform_frontend_feed_deactivated_{0}".gformFormat(r.options.formId),t,r.options.formId),gform.doAction("gform_{0}_frontend_feed_deactivated".gformFormat(t.addonSlug),t,r.options.formId),gform.doAction("gform_{0}_frontend_feed_deactivated_{0}".gformFormat(t.addonSlug,r.options.formId),t,r.options.formId))}},r.hasPriority=function(o,e){for(var t=r.getFeedsByAddon(e),d=0;d<=t.length;d++){var n=t[d];if(n.feedId!=o&&n.isActivated)return!1;if(n.feedId==o)return!0}return!1},this.init()};