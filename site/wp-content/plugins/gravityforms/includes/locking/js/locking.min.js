((t,s)=>{s(document).ready(function(){t.init()});var a,e,i,n,r,l,u=!1;function g(){s("#gform-lock-request-status").html(gform.utils.escapeHtml(i.noResponse)),s("#gform-lock-request-button").attr("disabled",!1).text(i.requestAgain),l=!!(u=!1),wp.heartbeat.interval(30)}t.init=function(){var c,o;n=gflockingVars.hasLock,a=gflockingVars.objectID,e=gflockingVars.objectType,r=gflockingVars.lockUI,i=gflockingVars.strings,wp.heartbeat.interval(30),s("#wpwrap").append(r),c="gform-refresh-lock-"+e,o="gform-request-lock-"+e,s(document).on("heartbeat-send."+c,function(t,e){var r={};a&&s("#gform-lock-dialog").length&&0!=n&&(r.objectID=a,e[c]=r)}),s(document).on("heartbeat-send."+o,function(t,e){var r={};if(!u)return e;r.objectID=a,e[o]=r}),s(document).on("heartbeat-tick."+c,function(t,e){var r,o,a;e[c]&&((e=e[c]).lock_error||e.lock_request)&&(a=e.lock_error||e.lock_request,(r=s("#gform-lock-dialog")).length)&&(r.is(":visible")?e.lock_error?s("#gform-reject-lock-request-button").is(":visible")&&(e.lock_error.avatar_src&&(o=s('<img class="avatar avatar-64 photo" width="64" height="64" />').attr("src",e.lock_error.avatar_src.replace(/&amp;/g,"&")),r.find("div.gform-locked-avatar").empty().append(o)),s("#gform-reject-lock-request-button").hide(),r.show().find(".currently-editing").text(e.lock_error.text)):e.lock_request&&s("#gform-lock-request-status").html(gform.utils.escapeScripts(e.lock_request.text)):(a.avatar_src&&(o=s('<img class="avatar avatar-64 photo" width="64" height="64" />').attr("src",a.avatar_src.replace(/&amp;/g,"&")),r.find("div.gform-locked-avatar").empty().append(o)),r.show().find(".currently-editing").text(a.text),e.lock_request?s("#gform-reject-lock-request-button").show():s("#gform-reject-lock-request-button").hide(),r.find(".wp-tab-first").focus()))}),s(document).on("heartbeat-tick."+o,function(t,e){if(e[o]&&(e=e[o]).status&&(e=e.status,s("#gform-lock-dialog").length))switch("pending"!=e&&(clearTimeout(l),u=l=!1),e){case"granted":s("#gform-lock-request-status").html(i.gainedControl),s("#gform-take-over-button").show(),s("#gform-lock-request-button").hide(),n=!0;break;case"deleted":s("#gform-lock-request-button").text(i.requestAgain).attr("disabled",!1),s("#gform-lock-request-status").html(i.rejected);break;case"pending":s("#gform-lock-request-status").html(i.pending)}}),s("#gform-lock-request-button").click(function(){var t=s(this);t.text("Request sent"),t.attr("disabled",!0),s("#gform-lock-request-status").html(""),u=!!1,wp.heartbeat.interval(5),l=setTimeout(g,12e4),s.getJSON(ajaxurl,{action:"gf_lock_request_"+e,object_id:a}).done(function(t){s("#gform-lock-request-status").html(gform.utils.escapeScripts(t.html))}).fail(function(t,e,r){e=e+", "+r;s("#gform-lock-request-status").html(gform.utils.escapeScripts(i.requestError+": "+e))})}),s("#gform-reject-lock-request-button").click(function(){s.getJSON(ajaxurl,{action:"gf_reject_lock_request_"+e,object_id:a,object_type:e}).done(function(t){s("#gform-lock-dialog").hide()}).fail(function(t,e,r){e=e+", "+r;s("#gform-lock-request-status").html(gform.utils.escapeScripts(i.requestError+": "+e)),s("#gform-lock-dialog").hide()})})}})(window.gflocking=window.gflocking||{},jQuery);