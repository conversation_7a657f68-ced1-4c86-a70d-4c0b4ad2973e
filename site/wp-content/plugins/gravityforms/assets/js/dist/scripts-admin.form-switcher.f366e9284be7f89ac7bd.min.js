"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[355],{4832:function(e,t,n){n.r(t),n.d(t,{default:function(){return w}});var r,o=n(5798),i=n(9662),s=n.n(i),a=n(1533),c=n.n(a),l=n(7122),d=n.n(l),u=(null===c()||void 0===c()||null===(r=c().components)||void 0===r?void 0:r.form_switcher)||{},f=function(e){s().instances.postSelects=[],e.forEach(function(e){var t;t=u.endpoints,s().instances.formSwitcher=new(s().components.admin.html.elements.Dropdown)({detectTitleLength:!0,onItemSelect:m,onItemSelectTimedOut:p,onOpen:v,reveal:"hover",selector:"gform-form-switcher",showSpinner:!0,searchType:"async",baseUrl:d(),endpoints:t,endpointKey:"get_forms",endpointRequestOptions:{method:"POST"},endpointUseRest:!1})})};function g(e,t){var n="";""===t&&(t=document.location.search.substring(1));for(var r=t.split("&"),o=0;o<r.length;o++){var i=r[o].split("=");i[0]!==e&&(n+=i[0]+"="+i[1]+"&")}return n.length>0&&(n=n.substring(0,n.length-1)),n}function m(e){if(e.length>0){var t=function(e,t){for(var n="",r=document.location.search.substring(1).split("&"),o=!1,i=0;i<r.length;i++){var s=r[i].split("=");s[0]===e?(n+=e+"="+t+"&",o=!0):"display_settings"!==s[0]&&(n+=s[0]+"="+s[1]+"&")}return n.length>0&&(n=n.substring(0,n.length-1)),o||(n+=n.length>0?"&"+e+"="+t:"?"+e+"="+t),n}("id",e=parseInt(e)),n=document.querySelector(".gform-dropdown__trigger[data-value='".concat(e,"']")),r=g("paged",t);r=g("s",r=r.replace("gf_new_form","gf_edit_forms")),r=g("operator",r),r=g("type",r),r=g("field_id",r),r=g("lid",r),r=g("fid",r),r=g("cid",r),r=g("nid",r),r=g("filter",r),r=g("pos",r);var o=new URLSearchParams(r).get("subview");if(o){var i=n?JSON.parse(n.dataset.subviews):null;i&&!i.includes(o)&&(r=g("subview",r))}var s=r.indexOf("gf_results_")>=0;r.indexOf("page=gf_entries")>=0&&!s&&(r="page=gf_entries&id="+e),s&&(r=n&&""!==n.dataset.resultsSlug&&void 0!==n.dataset.resultsSlug?"page=gf_entries&view=gf_results_".concat(n.dataset.resultsSlug,"&id=").concat(e):"page=gf_entries&id=".concat(e)),document.location="?"+r}}function p(e){if(e.length>0){var t=document.getElementById("gform-form-switcher-control"),n=t.querySelector('[data-js="gform-dropdown-control-text"]'),r=t.getAttribute("data-value-previous");if(r){var o=document.querySelector('.gform-dropdown__trigger-text[data-value="'+r+'"]');t.setAttribute("data-value",r),o&&(n.innerText=o.innerText)}}}function v(){var e=document.getElementById("gform-form-switcher-control"),t=e.getAttribute("data-value");t&&e.setAttribute("data-value-previous",t)}var h=function(e){f(e)},w=function(e){h(e),(0,o.consoleInfo)("Gravity Forms Admin: Initialized form switcher dropdown component.")}}}]);