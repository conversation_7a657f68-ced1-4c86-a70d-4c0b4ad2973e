"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[610],{3865:function(e,t,n){n.r(t),n.d(t,{default:function(){return g}});var s,o=n(5798),i=n(7195),c=n.n(i),r=n(9662),a=n.n(r),d=n(1533),l=n.n(d),p=(null===l()||void 0===l()||null===(s=l().components)||void 0===s?void 0:s.post_select)||{},u=function(e){var t=(0,o.getClosest)(e,".gform-settings-field"),n=e.dataset.postType,s=p[n],i=s.endpoints,r=s.data;a().instances.postSelects.push(new(c())({container:t.id,selector:"gform-settings-field-select",render:!1,renderListData:!0,searchType:"async",onItemSelect:function(e){return function(e,t){(0,o.getNodes)("gf-post-select-input",!0,t,!1)[0].value=e}(e,t)},baseUrl:i.get,endpoints:i,endpointKey:"get",endpointRequestOptions:{method:"GET"},endpointUseRest:!0,listData:r}))},f=function(e){!function(e){a().instances.postSelects=[],e.forEach(function(e){u(e)})}(e)},g=function(e){f(e),(0,o.consoleInfo)("Gravity Forms Admin: Initialized post select dropdown component.")}}}]);