"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[157],{3445:function(e,t,n){n.r(t),n.d(t,{default:function(){return L}});var r=n(5798),i=n(455),u=n(9280),c=n.n(u),o=n(3771),a=n(1295),d=function(e,t){switch(e.inputType){case"select":return g(e,t,"value");case"radio":return v(e,t,"value");default:return e.label}},p=function(e,t){switch(e.inputType){case"select":return g(e,t,"price");case"price":return(0,r.getNode)(".ginput_amount",t,!0).value;case"calculation":return(0,r.getNode)(".ginput_calculated_price",t,!0).value;case"radio":return v(e,t,"price");default:return e.basePrice?e.basePrice:0}},l=function(e){return"hidden"===e.getAttribute("data-conditional-logic")},f=function(e,t){return(0,r.getNode)("#field_".concat(e.dataset.formid,"_").concat(t),e,!0)},s=function(e,t){if(!e)return null;var n=e.split("|")[0],r=t.choices.filter(function(e){return e.value===n});return r&&r.length?r[0]:null},g=function(e,t,n){var i=(0,r.getNode)("select",t,!0),u=s(i.options[i.selectedIndex].value,e);return u?u[n]:""},v=function(e,t,n){var i=(0,r.getNodes)("input",!0,t,!0).find(function(e){return e.checked}),u=i?s(i.value,e):null;return u?u[n]:""},h=function(e,t,n){var r=[];return e.options?(Object.keys(e.options).forEach(function(i){var u=e.options[i],c=f(t,i);"checkbox"===u.inputType?r=r.concat(m(u,c,e.label,n)):_(u,c)&&r.push({id:i,name:d(u,c),field_label:e.label,price:n.toNumber(p(u,c))})}),r):[]},m=function(e,t,n,i){if(!t)return[];if(l(t))return[];var u=(0,r.getNodes)(".gfield_checkbox input",!0,t,!0);if(!u.length)return[];var c=[];return u.forEach(function(t){if(t.checked){var r=s(t.value,e);if(r){var u={id:String(e.id),name:r.value,field_label:n,price:i.toNumber(r.price)};c.push(u)}}}),c},_=function(e,t){return!!t&&(!l(t)&&!!d(e,t))},b=function(e,t,n){var r=f(t,e.id);return!!y(e,r)&&{id:String(e.id),name:d(e,r),price:n.toNumber(p(e,r))}},y=function(e,t){return!!t&&(!l(t)&&!!d(e,t))},N=n(2557),k=function(e){var t=e.detail.formId;I(t)},x=function(e){var t=P(e.target),n=w(e.target),i=e.target;(0,r.trigger)({event:"gform/products/product_field_changed",data:{event:e,formId:t,productFieldId:n,htmlInput:i},native:!1}),I(t)},w=function(e){var t=e.closest(".gfield"),n=t.className.split(" ").find(function(e){return e.startsWith("gfield_price_")});return void 0===n?t.id.split("_").pop():n.split("_").pop()},I=function(){var e=(0,i.A)(c().mark(function e(t){var n,i,u,l,f,s,g;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=new a.A(window.gf_global.gf_currency_config),i=(0,r.getNode)("#gform_".concat(t),document,!0)){e.next=4;break}return e.abrupt("return");case 4:return e.next=6,(0,N.x)("gform_theme_config/common/form/product_meta",t);case 6:if(u=e.sent,l=u?u.products:null){e.next=10;break}return e.abrupt("return");case 10:return f={products:[]},Object.keys(l).forEach(function(e){var u=l[e],c=(0,r.getNode)("#field_".concat(t,"_").concat(e),i,!0);c&&E(u,c,i)&&f.products.push({id:e,name:d(u,c),price:n.toNumber(p(u,c)),quantity:F(u,c,i),options:h(u,i,n)})}),(s=!!u.shipping&&b(u.shipping,i,n))&&(f.shipping=s),e.next=16,(0,r.filter)({event:"gform/products/product_changed",data:{formId:t,products:f}});case 16:g=e.sent,f=g.products,t=g.formId,o.hZ(t,"products",f);case 20:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),E=function(e,t,n){if(!t)return!1;if(l(t))return!1;var r=d(e,t),i=p(e,t),u=F(e,t,n);return r&&""!==i&&u},F=function(e,t,n){var i=(0,r.getNode)(".gfield_quantity_".concat(n.dataset.formid,"_").concat(e.id),n,!0),u=i?(0,r.getNode)("input, select",i,!0):(0,r.getNode)(".ginput_quantity",t,!0);return u?u.value?parseFloat(u.value):0:1},P=function(e){var t=e.closest("form");return t?t.dataset.formid:null};window.gform.products={getPaymentAmount:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=o.Jt(e,"products");if(0===(null==r||null===(t=r.products)||void 0===t?void 0:t.length))return 0;for(var i=0,u=0;u<r.products.length;u++){for(var c=r.products[u],a=c.price,d=0;d<c.options.length;d++)a+=c.options[d].price;if(a*=c.quantity,n&&parseFloat(n)===parseFloat(c.id))return a;i+=a}return r.shipping&&(i+=r.shipping.price),r.discount&&(i-=r.discount),i},handleConditionalLogicExecuted:k,handleProductChange:x,updateProducts:I};var q=n(428),A=n.n(q),C=function(e,t,n){document.addEventListener(e,function(e){e.target.closest(t)&&n.call(e.target,e)})},L=function(e){I(e),document.addEventListener("gform/conditionalLogic/applyRules/end",k),C("input",'.gfield_price input[type="text"], .gfield_price input[type="number"]',x),C("change",'.gfield_price input[type="text"], .gfield_price input[type="number"]',x),A()(document).on("change",".gfield_price select",x),C("change",".ginput_calculated_price",x),C("click",'.gfield_price input[type="radio"], .gfield_price input[type="checkbox"]',x),(0,r.consoleInfo)("Gravity Forms Theme: Initialized product field.")}}}]);