"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[42],{205:function(e,t,i){i.r(t),i.d(t,{default:function(){return ve}});var n,o=i(5518),r=i(2340),a=i.n(r),s=i(7329),l=i.n(s),c=i(5862),d=i.n(c),u={labels:(0,o.getNodes)("choices-ui-label",!0),mainSettings:(0,o.getNodes)('[data-js="choices-ui-setting"][data-type="main"]',!0,document,!0),sections:(0,o.getNodes)("choices-ui-section",!0),settings:(0,o.getNodes)("choices-ui-setting",!0),flyoutTrigger:(0,o.getNodes)("choices-ui-trigger")[0],flyoutTriggerLabel:(0,o.getNodes)("choices-ui-trigger-label")[0]},f=(null===l()||void 0===l()||null===(n=l().form_editor)||void 0===n?void 0:n.choices_ui)||{},m=function(){var e=(0,o.getNodes)('[data-js="choices-ui-setting"][data-type="main"]',!0,a().instances.choicesUi.flyout.elements.content,!0).filter((function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")}))[0],t=(0,o.getNodes)('[data-js="choices-ui-section"][data-type="options"]',!1,e,!0)[0],i=(0,o.getNodes)("choices-ui-option-list",!1,t)[0];t.style.display="block",t.style.display=i.clientHeight>25?"block":"none"},v=function(){var e,t,i,n,r,s=u.settings.filter((function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")}));u.flyoutTrigger.style.display=s.length?"":"none",u.flyoutTriggerLabel.style.display=s.length?"":"none",function(){if(!(u.mainSettings.length<2)){var e=u.mainSettings.filter((function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")}))[0];if(e){var t=(0,o.getNodes)("choices-ui-option-list",!1,e)[0];t.innerHTML="",u.options.forEach((function(e){t.appendChild(e),"list-item"===window.getComputedStyle(e).getPropertyValue("display")&&(e.style.display="inline-block")}))}}}(),i=(null===(e=window)||void 0===e||null===(e=e.field)||void 0===e?void 0:e.type)||"",n=(null===(t=window)||void 0===t||null===(t=t.field)||void 0===t?void 0:t.inputType)||"",r=a().instances.choicesUi.flyout.elements.flyout,(0,o.removeClassThatContains)(r,"gform-flyout--choices-ui--"),i&&r.classList.add("gform-flyout--choices-ui--".concat(i)),n&&r.classList.add("gform-flyout--choices-ui--input-type-".concat(n)),m()},g=function(e){var t=e.title,i=void 0===t?"":t,n=e.content,r=void 0===n?null:n,s=e.position,l=void 0===s?"beforeend":s,c=e.type,d=void 0===c?"":c;if(r){a().instances.choicesUi.flyout.elements.content.insertAdjacentHTML(l,'\n\t\t<div class="choices-ui__section" data-js="choices-ui-section" data-type="'.concat(d,'">\n\t\t\t<h6 class="choices-ui__section-label">').concat((0,o.escapeHtml)(i),"</h6>\n\t\t</div>\n\t"));var f=(0,o.getNodes)("choices-ui-section",!0);f[f.length-1].appendChild(r),u.sections.push(f[f.length-1])}else(0,o.consoleError)("Gravity Forms Admin: You must supply a valid node to appendSectionHtml.")},h=function(e){u.container=e,(0,o.trigger)({event:"gform/choices_ui/pre_init",native:!1,data:{elements:u}}),a().instances=a().instances||{},a().instances.choicesUi={},function(){var e=f.i18n,t=e.title,i=e.expandableTitle,n=e.description;a().instances.choicesUi.flyout=new(d())({description:(0,o.escapeHtml)(n),expandable:!0,expandableTitle:(0,o.escapeHtml)(i),expandableWidth:100,id:"choices-ui-flyout",maxWidth:540,mobileBreakpoint:1200,onOpen:function(){setTimeout((function(){m()}),50)},position:"absolute",simplebar:!0,target:'[data-js="form-editor"]',title:(0,o.escapeHtml)(t),triggers:'[data-js="choices-ui-trigger"]',wrapperClasses:"gform-flyout gform-flyout--choices-ui",zIndex:100})}(),function(){a().instances.choicesUi.flyout.elements.content.insertAdjacentHTML("afterbegin",'<ul class="choices-ui__content" data-js="choices-ui-content"></ul>');var e=(0,o.getNodes)("choices-ui-content",!1,a().instances.choicesUi.flyout.elements.content)[0];u.settings.forEach((function(t){return e.appendChild(t)}))}(),u.optionsList=(0,o.getNodes)("choices-ui-option-list",!1,a().instances.choicesUi.flyout.elements.content)[0],u.settings.forEach((function(e){"option"===e.dataset.type&&u.optionsList.appendChild(e)})),u.options=(0,o.getNodes)('[data-js="choices-ui-option-list"] > li',!0,a().instances.choicesUi.flyout.elements.content,!0),a().instances.choicesUi.flyout.elements.flyout.addEventListener("click",(function(e){e.stopPropagation()})),document.addEventListener("gform/form_editor/setting_selected",v),u.flyoutEl=(0,o.getNodes)("#choices-ui-flyout .gform-flyout__body",!1,document,!0),a().instances.choicesUi.elements=u,a().instances.choicesUi.methods={appendSectionHtml:g},(0,o.trigger)({event:"gform/choices_ui/post_render",native:!1,data:a().instances.choicesUi}),(0,o.consoleInfo)("Gravity Forms Admin: Initialized choices ui flyout.")},p=function(e){h(e),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all choices ui scripts.")},y=function(e){var t=e.detail.field;document.querySelector(".gform-compact-view")&&(t.classList.add("trigger-reflow"),t.offsetHeight,t.classList.remove("trigger-reflow"))},w=function(){document.addEventListener("gform/layout_editor/field_refresh_preview",y),(0,o.consoleInfo)("Gravity Forms Admin: Initialized form editor field event listeners.")},S=function(){w(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all form editor field scripts.")},b=i(6588),_=i(9137),E=i(5952),k=i(3004),A=i(9668),F=i(1010),C=i(6140),j=i(5311),I=i.n(j);function N(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return T(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return T(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){s=!0,r=e},f:function(){try{a||null==i.return||i.return()}finally{if(s)throw r}}}}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function x(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=(0,C.Z)(e);if(t){var o=(0,C.Z)(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return(0,F.Z)(this,i)}}var B=function(e){(0,A.Z)(i,e);var t=x(i);function i(){return(0,_.Z)(this,i),t.apply(this,arguments)}return(0,E.Z)(i,[{key:"init",value:function(){var e=this;this.eventsManager.addListener("SaveRequested",this.save,this),this.eventsManager.addListener("SaveCompleted",this.resetFormChars,this),document.addEventListener("gform/dialog/confirm",this.maybeSave.bind(this));var t,i=N(this.config.data.domEvents);try{var n,o=function(){var i=t.value;if(i){var n=e.eventsManager.get(i.name),o=document,r={};if("document"!==i.elementSelector&&(o=document.getElementsByClassName(i.elementSelector.replace(".",""))[0]),void 0===o)return{v:void 0};o.addEventListener(i.action,(function(t){if(!e.saveInProgress){if("keydown"===i.action&&"keys"in i&&i.keys.length>0){if(-1===i.keys.indexOf(t.keyCode))return!1;if(r[t.keyCode.toString()]=t.keyCode,!i.keys.every((function(e){return e in r!=!1})))return e.pressedKeysTimeOut=setTimeout((function(){r={}}),1e3),!1}e.saveInProgress||(a().instances.adminFormSaverUIHandler.activeElement=document.activeElement,document.activeElement.blur(),t.preventDefault(),t.stopImmediatePropagation(),r={},n.fire(),e.saveInProgress=!0)}}))}};for(i.s();!(t=i.n()).done;)if(n=o())return n.v}catch(e){i.e(e)}finally{i.f()}}},{key:"maybeSave",value:function(e){if("gform/dialog/confirm"===e.type){var t;if("dialog-embed-form-unsaved-changes"!==(null==e||null===(t=e.detail)||void 0===t||null===(t=t.instance)||void 0===t||null===(t=t.options)||void 0===t?void 0:t.id))return;this.save()}}},{key:"save",value:function(){var e=(0,o.getNodes)("force-focus")[0];if(e&&e.focus(),this.formJSONString=this.getUpdatedFormJSON(),!window.ValidateForm())return this.eventsManager.trigger("SaveAborted"),!1;(0,k.Z)((0,C.Z)(i.prototype),"save",this).call(this)}},{key:"deepSanitizeFormValues",value:function(e){if("object"!==(0,b.Z)(e))return e;for(var t in e){var i=e[t];"object"!==(0,b.Z)(i)||null===i?"string"==typeof i?(e[t]=i.replace(/\\'/g,"'"),e[t]=i.replace(/\\/g,"\\\\")):e[t]=i:e[t]=this.deepSanitizeFormValues(i)}return e}},{key:"resetFormChars",value:function(){window.form=this.reverseFormSanitization(this.form)}},{key:"reverseFormSanitization",value:function(e){if("object"!==(0,b.Z)(e))return e;for(var t in e){var i=e[t];"object"!==(0,b.Z)(i)||null===i?e[t]="string"==typeof i?i.replace(/\\\\/g,"\\"):i:e[t]=this.reverseFormSanitization(i)}return e}},{key:"getUpdatedFormJSON",value:function(){try{this.form=window.UpdateFormObject(),window.gforms_original_json=I().toJSON(this.form)}catch(e){(0,o.consoleError)(e)}var e=this.deepSanitizeFormValues(this.form);return I().toJSON(e)}}]),i}(i(5192).Z),L=i(9885),D=i(5998),O=i.n(D),U=i(3068),R=i.n(U),H=i(1519),P=i.n(H);function Z(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return M(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return M(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){s=!0,r=e},f:function(){try{a||null==i.return||i.return()}finally{if(s)throw r}}}}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var z=function(){function e(t,i){(0,_.Z)(this,e),this.events=t,this.config=i,this.timeOuts=[],this.scripts=[],this.styles=[],this.buttons=[],this.currentFormFields={},this.storeCurrentFormFields(),this.maybeHandleNormalPostRequest(),this.setInitialUrlState(),this.initButtons(),this.bindEvents(),this.init()}return(0,E.Z)(e,[{key:"storeCurrentFormFields",value:function(){var e=this;window.form.fields.forEach((function(t){e.currentFormFields[t.id]=JSON.stringify(t)}))}},{key:"init",value:function(){var e=this;this.timeOuts.forEach((function(e){window.clearTimeout(e)})),document.getElementsByTagName("script").forEach((function(t){var i=t.getAttribute("src");null!==i&&(i=i.substring(0,i.indexOf("?ver")),e.scripts.push(i))})),document.getElementsByTagName("link").forEach((function(t){var i=t.getAttribute("href");null!==i&&(i=i.substring(0,i.indexOf("?ver")),e.styles.push(i))})),this.selectedField={}}},{key:"initButtons",value:function(){var e=this;this.config.data.selectors.saveAnimationButtons.forEach((function(t){(0,o.getNodes)(t,!0,document,!0).forEach((function(t){var i=t.id?t.id:(0,o.uniqueId)("button-");t.id=i;var n=new(R())({activeType:"loader",id:i,interactive:!0,loaderOptions:{background:"transparent",foreground:"#fff",mask:!1,showOnRender:!1,size:1.5},rendered:!0});e.buttons.push(n)}))}))}},{key:"maybeHandleNormalPostRequest",value:function(){void 0!==window.updateFormResult&&("invalid_json"===window.updateFormResult.status?this.showSaveErrorNotification():this.showSaveSuccessNotification())}},{key:"bindEvents",value:function(){this.events.addListeners([{name:"SaveBegan",handler:[this.init,this.beginButtonAnimation]},{name:"SaveInProgress",handler:this.handleSaveProgress},{name:["SaveFailed","SaveRequestFailed","SaveResponseMalformed"],handler:[this.handleSaveFailure]},{name:"SaveSucceeded",handler:[this.handleSaveSuccess]},{name:"SaveCompleted",handler:this.reset},{name:"SaveBegan",handler:this.beginButtonAnimation},{name:["SaveCompleted","SaveAborted"],handler:[this.endButtonAnimation,this.reset]}],this)}},{key:"beginButtonAnimation",value:function(){this.buttons.filter((function(e){return e.options.interactive})).forEach((function(e){return e.activateButton()}))}},{key:"endButtonAnimation",value:function(){this.buttons.filter((function(e){return e.options.interactive})).forEach((function(e){return e.deactivateButton()}))}},{key:"reset",value:function(){var e=this;this.registerTimeOut((function(){e.buttons.forEach((function(e){e.deactivateButton()})),"id"in e.selectedField&&I()("#field_"+e.selectedField.id).click(),e.resetSettingsControlsDisabledState(),e.activeElement&&I()(e.activeElement).focus(),a().instances.adminFormSaver.saveInProgress=!1}),this.config.data.animationDelay)}},{key:"resetElementClasses",value:function(e,t){var i=t.classList.value;if(void 0!==i&&""!==i){i=i.split(" "),e.removeClass();for(var n=0;n<i.length;n++)""!==i[n]&&void 0!==i[n]&&e.addClass(i[n])}}},{key:"handleSaveProgress",value:function(){var e=this;this.selectedField=window.GetSelectedField(),this.selectedField||(this.selectedField={}),this.registerTimeOut((function(){O().a11y.speak(e.config.i18n.saveInProgress,"polite"),e.disableSettingsControls()}),this.config.data.animationDelay/8)}},{key:"handleSaveFailure",value:function(e){var t=this;this.registerTimeOut((function(){var i,n=null==e||null===(i=e.error)||void 0===i?void 0:i.message;n&&"Failed to fetch"===n?t.showSaveErrorNotification(t.config.i18n.networkError):t.showSaveErrorDialog(),(0,o.trigger)({event:"gform/form_editor_saver/post_save_error",native:!1})}),this.config.data.animationDelay)}},{key:"fireManualSave",value:function(){(0,o.getNodes)("#gform_export",!1,document,!0)[0].value=!0,window.SaveForm()}},{key:"showSaveErrorDialog",value:function(){new(P())({animationDelay:250,closeOnConfirmClick:!1,closeOnMaskClick:!1,confirmButtonIcon:"floppy-disk",onConfirm:this.fireManualSave.bind(this),id:"dialog-ajax-save-error",cancelButtonText:this.config.i18n.ajaxErrorDialogCancelButtonText,closeButtonTitle:this.config.i18n.ajaxErrorDialogCloseButtonTitle,confirmButtonText:this.config.i18n.ajaxErrorDialogConfirmButtonText,content:this.config.i18n.ajaxErrorDialogContent,title:this.config.i18n.ajaxErrorDialogTitle,titleIcon:"circle-delete",mode:"dialog",titleIconColor:"#DD301D",wrapperClasses:"gform-dialog ",zIndex:1e5}).showDialog()}},{key:"handleSaveSuccess",value:function(e){var t=this;this.registerTimeOut((function(){t.showSaveSuccessNotification(),t.updatePageURL(),(0,o.trigger)({event:"gform/form_editor_saver/post_save_success",native:!1})}),this.config.data.animationDelay),this.reloadEditorUI(e)}},{key:"setInitialUrlState",value:function(){window.history.replaceState(window.form,document.title,(0,o.updateQueryVar)("gf_ajax_save",null))}},{key:"updatePageURL",value:function(){window.history.replaceState({},document.title,(0,o.updateQueryVar)("gf_ajax_save",(0,o.uniqueId)(""),(0,o.updateQueryVar)("isnew",null)))}},{key:"showSaveSuccessNotification",value:function(){var e=this.config.data.urls.formPreview,t=this.config.i18n,i=t.formUpdated,n=t.viewForm;(0,o.trigger)({data:{autoHideDelay:4500,container:"#form_editor_fields_container",ctaLink:(0,o.escapeHtml)((0,o.vsprintf)(e,[window.form.id])),ctaTarget:"_blank",ctaText:(0,o.escapeHtml)(n),icon:"circle-check-alt",message:(0,o.escapeHtml)(i),type:"success"},event:"gform/snackbar/render",native:!1})}},{key:"showSaveErrorNotification",value:function(e){var t=this.config.i18n.genericError,i="string"==typeof e&&""!==e&&null!==e?e:t;(0,o.trigger)({data:{autoHideDelay:4500,container:"#form_editor_fields_container",icon:"circle-delete",message:(0,o.escapeHtml)(i),type:"error"},event:"gform/snackbar/render",native:!1})}},{key:"disableSettingsControls",value:function(){document.querySelectorAll(".field_setting input, .field_setting select, .field_setting textarea").forEach((function(e){e.setAttribute("data-js-initial-disabled-state",e.disabled),e.disabled=!0}))}},{key:"resetSettingsControlsDisabledState",value:function(){document.querySelectorAll(".field_setting input, .field_setting select, .field_setting textarea").forEach((function(e){"false"===e.getAttribute("data-js-initial-disabled-state")&&e.removeAttribute("disabled"),e.removeAttribute("data-js-initial-disabled-state")}))}},{key:"reloadEditorUI",value:function(e){try{var t=document.createElement("html");t.innerHTML=decodeURIComponent(e.data.updated_markup.toString());var i=I()(t);this.reloadSettings(i),this.reloadScripts(i),this.reloadElements(i)}catch(e){}}},{key:"reloadScripts",value:function(e){var t=this;e.find("script[data-js-reload]").toArray().forEach((function(e){var i=I()(e).attr("data-js-reload"),n=I()('script[data-js-reload="'+i+'"]');if(n.length){var o=t.events.trigger("scriptBeforeReload",e,i,n.get(0));!1!==o&&void 0!==I()(o).attr("data-js-skip-reload")&&(n.replaceWith(o),t.events.trigger("scriptAfterReload",o,i,n.get(0)))}})),I()("#gform_editor_js_action_output_wrapper script").toArray().forEach((function(e){e.remove()}));var i=e.find("#gform_editor_js_action_output_wrapper").toArray().pop();!1!==(i=this.events.trigger("gformEditorJSActionBeforeReload",i))&&I()("#gform_editor_js_action_output_wrapper").replaceWith(i)}},{key:"reloadElements",value:function(e){var t=this;(0,o.getNodes)(":not(script)[data-js-reload]",!0,e[0],!0).forEach((function(e){var i=e.getAttribute("data-js-reload");if("true"!==i){var n=(0,o.getNodes)(':not(script)[data-js-reload="'.concat(i,'"]'),!1,document,!0)[0];if(n&&t.shouldReplaceElement(n,e)){(0,o.consoleInfo)("updating element: "+i);var r=t.events.trigger("elementBeforeReload",e,i,n);!1===r||r.getAttribute("data-js-skip-reload")||(I()(n).replaceWith(r),t.events.trigger("elementAfterReload",r,i,n))}}})),"id"in this.selectedField&&I()("#field_"+this.selectedField.id).addClass("field_selected")}},{key:"shouldReplaceElement",value:function(e,t){if(!1===e.classList.contains("gfield"))return(0,o.consoleInfo)("Element with ID: "+e.getAttribute("id")+" is not a field element, its markup should be updated"),!0;var i=parseInt(t.getAttribute("id").replace("field_","")),n=this.currentFormFields[i],r=window.form.fields.reduce((function(e,t){return t.id!==i?e:JSON.stringify(t)}),!1);return r&&r!==n}},{key:"reloadSettings",value:function(e){var t,i=I()(".editor-sidebar"),n=I()(".gform-flyout"),o=e.find(".editor-sidebar"),r=[],a=[],s=[],l=o.find("li.field_setting").toArray(),c=i.find("li.field_setting").toArray(),d=n.find("li.field_setting").toArray(),u=this.getElementsAsClassLists(l),f=this.getElementsAsClassLists(d),m=this.getElementsAsClassLists(c),v=Z(m=m.concat(f));try{for(v.s();!(t=v.n()).done;){var g=t.value;u.indexOf(g)>=0||a.push(g)}}catch(e){v.e(e)}finally{v.f()}var h,p=Z(u);try{for(p.s();!(h=p.n()).done;){var y=h.value;""!==y&&(m.indexOf(y)>=0?s.push(y):r.push(y))}}catch(e){p.e(e)}finally{p.f()}this.insertNewFieldSettings(r,o),this.updateExistingFieldSettings(s,o,e),this.deleteExpiredFieldSettings(a)}},{key:"insertNewFieldSettings",value:function(e,t){var i,n=Z(e);try{for(n.s();!(i=n.n()).done;){var o=i.value,r=t.find("."+o).prevUntil().toArray();if(this.events.trigger("beforeFieldSettingAdded",[I()("."+o).get(0),t.find("."+o).get(0)]),r.length<1){var a=t.find("."+o).parent().attr("id");I()("#"+a).prepend(t.find("."+o)),this.events.trigger("afterFieldSettingAdded",[o])}else{var s,l=Z(r);try{for(l.s();!(s=l.n()).done;){var c=s.value.classList.value.split(" ")[0];if(I()("."+c).length){t.find("."+o).insertAfter(I()("."+c)),this.events.trigger("afterFieldSettingAdded",[o]);break}}}catch(e){l.e(e)}finally{l.f()}}}}catch(e){n.e(e)}finally{n.f()}}},{key:"deleteExpiredFieldSettings",value:function(e){var t,i=Z(e);try{for(i.s();!(t=i.n()).done;){var n=t.value;this.events.trigger("beforeFieldSettingDeleted",[n]),I()("."+n).remove(),this.events.trigger("AfterFieldSettingDeleted",[n])}}catch(e){i.e(e)}finally{i.f()}}},{key:"updateExistingFieldSettings",value:function(e,t,i){var n=this;e.forEach((function(e){var o="."+e;I()(o).hasClass("data-js-reload")&&(n.events.trigger("fieldSettingBeforeUpdate",I()(o).get(0),t.find(o).get(0)),I()(o).replaceWith(i.find(o)),n.events.trigger("fieldSettingAfterUpdate",I()(o).get(0)))}))}},{key:"getElementsAsClassLists",value:function(e){return e.map((function(e){return e.classList.value.split(" ")[0]}))}},{key:"registerTimeOut",value:function(e,t){var i=setTimeout(e,t);return this.timeOuts.push(i),i}}]),e}(),V=(null===l()||void 0===l()?void 0:l().form_editor_save_form)||{};a().instances=(null===a()||void 0===a()?void 0:a().instances)||{};var q,G=function(){a().instances.formSaverEventsManager=new L.Z,a().instances.adminFormSaverUIHandler=new z(a().instances.formSaverEventsManager,V),a().instances.adminFormSaver=new B(V,{config:V,events:a().instances.formSaverEventsManager,endpointKey:"form_editor_save_form",form:window.form}),a().instances.adminFormSaver.init()},J=i(6796),W=i(5210),Q=i(8349),K=i(1547),$=function(e,t){var i,n;return function(){var o=this,r=arguments;n?(clearTimeout(i),i=setTimeout((function(){Date.now()-n>=t&&(e.apply(o,r),n=Date.now())}),t-(Date.now()-n))):(e.apply(o,r),n=Date.now())}},Y=Q.ReactDOM.createRoot,X={DeleteField:"delete",StartDuplicateField:"duplicate"},ee=((null===l()||void 0===l()||null===(q=l().components)||void 0===q?void 0:q.dropdown_menu)||{}).i18n,te=ee.duplicateButtonLabel,ie=ee.deleteButtonLabel,ne=ee.dropdownButtonLabel,oe=function(e){return function(t){var i=t.target.closest("[data-js-reload]");if(i){var n=i.getAttribute("data-js-reload").split("_"),o=(0,W.Z)(n,2),r=(o[0],o[1]),a=X[e]||e.toLowerCase(),s="gfield_".concat(a,"_").concat(r),l=document.getElementById(s);if(l&&"function"==typeof window[e]&&window[e](l),"StartDuplicateField"===e){var c=i.querySelector(".gform-button");c&&c.click()}}}},re={icon:"duplicate",label:te,customClasses:["gform-compact-view-overflow-menu__item","gform-compact-view-overflow-menu__item-duplicate"],customAttributes:{onClick:oe("StartDuplicateField")}},ae={icon:"trash",label:ie,customClasses:["gform-compact-view-overflow-menu__item","gform-compact-view-overflow-menu__item-delete"],customAttributes:{onClick:oe("DeleteField")}},se=["total","paypal","captcha","turnstile","post_title","post_content","post_excerpt","total","shipping","mollie","creditcard","submit"],le=function(e){var t=function(e){var t="gfield--type-";return document.getElementById("field_".concat(e.split("_").pop())).className.split(" ").find((function(e){return e.startsWith(t)})).substring(13)}(e);return!se.includes(t)},ce=function(){return document.getElementById("form_editor_fields_container").classList.contains("gform-compact-view")},de=function(e,t){if(t&&!e.getAttribute("data-dropdown-added"))!function(e){var t={align:"right",listItems:[le(e.id)&&Q.React.createElement(K.c,(0,J.Z)({},re,{key:"duplicate"})),Q.React.createElement(K.c,(0,J.Z)({},ae,{key:"delete"}))].filter(Boolean),type:"action",customClasses:["gform-compact-view-overflow-menu__container"],triggerAttributes:{icon:"ellipsis",size:"size-height-s",type:"simplified",ariaText:ne,ariaId:e.id},width:120,autoPosition:!0,customAttributes:{"aria-label":ne}};Y(e).render(Q.React.createElement(K.Z,t))}(e),e.setAttribute("data-dropdown-added","true");else if(!t&&e.getAttribute("data-dropdown-added")){Y(e).unmount(),e.removeAttribute("data-dropdown-added")}},ue=function(e){(0,o.getNodes)('[data-js="gform-compact-view-overflow-menu"]',!0,document,!0).forEach((function(t){return de(t,e)}))},fe=function(){ue(ce())},me=function(){var e;e=$(ue,200),ce()&&e(!0),["gform/layout_editor/field_modified","gform/form_editor/field-duplicated-native","gform/form_editor/compact-view-active","gform/form_editor/compact-view-inactive","gform/layout_editor/field_refresh_preview","gform/layout_editor/field_start_change_type"].forEach((function(e){return document.addEventListener(e,fe)})),(0,o.consoleInfo)("Gravity Forms Admin: Initialized dropdown component on event: gform/form_editor/compact-view-active")},ve=function(e){p(e),S(),G(),me(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all form editor scripts.")}}}]);