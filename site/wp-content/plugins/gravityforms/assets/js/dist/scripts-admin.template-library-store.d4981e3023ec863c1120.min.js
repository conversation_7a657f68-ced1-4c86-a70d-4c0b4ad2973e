"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[907],{9049:function(t,n,r){r.r(n);var e,u,o,i=r(8335),l=r(5798),f=r(1533),a=r.n(f),c=null===(e=(0,l.getConfig)(a(),"gform_admin_config"))||void 0===e||null===(e=e.components)||void 0===e?void 0:e.template_library,s=(null==c||null===(u=c.data)||void 0===u?void 0:u.defaults)||{};"mock_endpoint"===(null==c||null===(o=c.endpoints)||void 0===o||null===(o=o.create_from_template)||void 0===o?void 0:o.action)&&(s.isLibraryOpen=!0);var y=(0,i.create)(s,function(t){return{setIsLibraryOpen:function(n){return t(function(){return{isLibraryOpen:n}})},setFlyoutOpen:function(n){return t(function(){return{flyoutOpen:n}})},setFlyoutFooterButtonLabel:function(n){return t(function(){return{flyoutFooterButtonLabel:n}})},setFlyoutTitleValue:function(n){return t(function(){return{flyoutTitleValue:n}})},setFlyoutDescriptionValue:function(n){return t(function(){return{flyoutDescriptionValue:n}})},setSelectedTemplate:function(n){return t(function(){return{selectedTemplate:n}})},setFlyoutTitleErrorState:function(n){return t(function(){return{flyoutTitleErrorState:n}})},setFlyoutTitleErrorMessage:function(n){return t(function(){return{flyoutTitleErrorMessage:n}})},setImportError:function(n){return t(function(){return{importError:n}})},setFlyoutPrimaryLoadingState:function(n){return t(function(){return{flyoutPrimaryLoadingState:n}})}}});n.default=y}}]);