!function(){var e,t,r,n={379:function(e,t,r){var n={".":[9819],"./":[9819],"./index":[9819],"./index.js":[9819],"./setup-wizard":[1907,187,854,910,707,100,909,898,694,874],"./setup-wizard/":[1907,187,854,910,707,100,909,898,694,874],"./setup-wizard/Screen01":[9708,187,854,910],"./setup-wizard/Screen01.js":[9708,187,854,910],"./setup-wizard/Screen02":[9445,854,909],"./setup-wizard/Screen02.js":[9445,854,909],"./setup-wizard/Screen03":[7526,854,100],"./setup-wizard/Screen03.js":[7526,854,100],"./setup-wizard/Screen04":[4679,187,854,707],"./setup-wizard/Screen04.js":[4679,187,854,707],"./setup-wizard/Screen05":[528,854,898],"./setup-wizard/Screen05.js":[528,854,898],"./setup-wizard/index":[1907,187,854,910,707,100,909,898,694,874],"./setup-wizard/index.js":[1907,187,854,910,707,100,909,898,694,874],"./setup-wizard/setup-wizard":[516,187,854,910,707,100,909,898,694],"./setup-wizard/setup-wizard.js":[516,187,854,910,707,100,909,898,694],"./setup-wizard/store":[7290,854],"./setup-wizard/store.js":[7290,854],"./template-library":[5584,187,215,907,752,441,528,647],"./template-library/":[5584,187,215,907,752,441,528,647],"./template-library/index":[5584,187,215,907,752,441,528,647],"./template-library/index.js":[5584,187,215,907,752,441,528,647],"./template-library/store":[9049,907],"./template-library/store.js":[9049,907],"./template-library/template-library":[7174,187,215,907,752,441,528],"./template-library/template-library-flyout":[7688,907,752],"./template-library/template-library-flyout.js":[7688,907,752],"./template-library/template-library-grid":[1159,187,215,441],"./template-library/template-library-grid.js":[1159,187,215,441],"./template-library/template-library.js":[7174,187,215,907,752,441,528],"./template-library/utils":[5078,187,215],"./template-library/utils.js":[5078,187,215]};function o(e){if(!r.o(n,e))return Promise.resolve().then(function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],o=t[0];return Promise.all(t.slice(1).map(r.e)).then(function(){return r(o)})}o.keys=function(){return Object.keys(n)},o.id=379,e.exports=o},395:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Checkbox},428:function(e){"use strict";e.exports=window.jQuery},484:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Button},1533:function(e){"use strict";e.exports=gform_admin_config},1862:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Textarea},2001:function(e){"use strict";e.exports=gf_vars},2045:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Flyout},2225:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Text},2279:function(e){"use strict";e.exports=window.wp.plugins},2312:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Alert},2368:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Select},2595:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Droplist},2685:function(e){"use strict";e.exports=window.gform.components.admin.html.modules.Flyout},3244:function(e){"use strict";e.exports=wp},3499:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Steps},3801:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.FileUpload},3841:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.InputGroup},3963:function(e){"use strict";e.exports=window.gform.components.admin.html.modules.Dialog},4309:function(e){"use strict";e.exports=window.wp.editPost},4496:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Label},4504:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Z}});var n,o=r(1533),a=r.n(o),l=r(1860),i=r(527),c=r(8140),s=r(5798),m=window.wp.blockEditor,u=window.wp.components,d=window.wp.serverSideRender,f=r.n(d),p=r(7616),g=r(6087),b=r(3963),h=r.n(b),_=window.wp.i18n,y=React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 508.3 559.5",width:"100%",height:"100%",focusable:"false","aria-hidden":"true",className:"dashicon dashicon-gravityforms"},React.createElement("g",null,React.createElement("path",{className:"st0",d:"M468,109.8L294.4,9.6c-22.1-12.8-58.4-12.8-80.5,0L40.3,109.8C18.2,122.6,0,154,0,179.5V380\tc0,25.6,18.1,56.9,40.3,69.7l173.6,100.2c22.1,12.8,58.4,12.8,80.5,0L468,449.8c22.2-12.8,40.3-44.2,40.3-69.7V179.6\tC508.3,154,490.2,122.6,468,109.8z M399.3,244.4l-195.1,0c-11,0-19.2,3.2-25.6,10c-14.2,15.1-18.2,44.4-19.3,60.7H348v-26.4h49.9\tv76.3H111.3l-1.8-23c-0.3-3.3-5.9-80.7,32.8-121.9c16.1-17.1,37.1-25.8,62.4-25.8h194.7V244.4z"}))),v=r(6342),w=["label","colors","color","controlled","defaultColor","onChange"];function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var x=null===a()||void 0===a()?void 0:a().block_editor["gravityforms/form"].i18n,E=(null===(n=window)||void 0===n?void 0:n.wp)||{},C=E.components,R=C.__experimentalHStack,N=C.__experimentalItemGroup,S=C.Button,j=C.ColorPalette,B=C.ColorIndicator,P=C.FlexItem,O=C.Dropdown,z=(Object.prototype.hasOwnProperty.call(E,"blockEditor")?E.blockEditor:E.editor).useSettings,I=E.element,T=I.useState,F=I.useEffect,M=N,A=R,L=function(e){var t=T(e),r=(0,c.A)(t,2),n=r[0],o=r[1];return F(function(){o(e)},[e]),[n,o]};function D(e){var t,r=e.label,n=e.colors,o=void 0===n?[]:n,a=e.color,l=e.controlled,s=void 0!==l&&l,m=e.defaultColor,u=e.onChange,d=(0,v.A)(e,w),f=(s?L:T)(a),g=(0,c.A)(f,2),b=g[0],h=g[1],_=z("color.palette.theme","color.palette.custom","color.palette.default","color.defaultPalette"),y=(0,c.A)(_,4),E=y[0],C=y[1],R=y[2],N=y[3],I=!0,F=function(){var e=[];return o.length&&e.push({name:"Orbital",colors:o}),C&&C.length&&e.push({name:x.custom_colors,colors:C}),E&&E.length&&e.push({name:x.theme_colors,colors:E}),N&&R&&R.length&&e.push({name:x.default_colors,colors:R}),e}(),D={colorValue:b,toggleLabel:r},H={className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__item-group":!0}),isBordered:!0,isSeparated:!0},V=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){(0,i.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({popoverProps:{placement:"left-end",position:"bottom left",className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__dropdown-content":!0})},className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__dropdown":!0}),renderToggle:(t=D,function(e){var r=e.onToggle,n=e.isOpen,o=t.colorValue,a=t.toggleLabel,l={onClick:r,className:(0,p.classnames)("block-editor-panel-color-gradient-settings__dropdown",{"is-open":n}),"aria-expanded":n};return React.createElement(S,l,function(e){var t=e.colorValue,r=e.indicatorLabel,n={justify:"flex-start"},o={className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__color-indicator":!0}),colorValue:t},a={className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__color-name":!0,title:r})};return React.createElement(A,n,React.createElement(B,o),React.createElement(P,a,r))}({colorValue:o,indicatorLabel:a}))}),renderContent:function(){var e={className:(0,p.classnames)({"block-editor-panel-color-gradient-settings__dropdown-content":!0})},t={colors:F,value:b,onChange:function(e){var t=!e&&m?m:e;h(t),u(t)},__experimentalHasMultipleOrigins:I};return React.createElement("div",e,React.createElement(j,t))}},d);return React.createElement(M,H,React.createElement(O,V))}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function V(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){(0,i.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var G=wp.url.addQueryArgs,U=u.__experimentalHeading,q=u.__experimentalText,$=u.__experimentalSpacer,J=(null===a()||void 0===a()?void 0:a().block_editor["gravityforms/form"])||{},Q=J.i18n,K=function(e){var t=e.attributes,r=e.setAttributes,n=(0,g.useState)(t.formId),o=(0,c.A)(n,2),a=o[0],d=o[1],b=(0,g.useState)(!1),v=(0,c.A)(b,2),w=v[0],k=v[1],x=(0,g.useState)(t.formPreview),E=(0,c.A)(x,2),C=E[0],R=E[1],N=(0,g.useState)(t.title),S=(0,c.A)(N,2),j=S[0],B=S[1],P=(0,g.useState)(t.description),O=(0,c.A)(P,2),z=O[0],I=O[1],T=(0,g.useState)(t.ajax),F=(0,c.A)(T,2),M=F[0],A=F[1],L=(0,g.useState)(t.defaults),H=(0,c.A)(L,2),K=H[0];H[1];(0,g.useLayoutEffect)(function(){if(!a&&(0,s.queryToJson)().gfAddBlock){var e=(0,s.queryToJson)().gfAddBlock;r({formId:e}),d(e)}},[]),(0,g.useLayoutEffect)(function(){if(a){if(!W(a))return r({formId:""}),void k(!0);r({formId:a}),R(!0)}else r({formId:""})},[a]),(0,g.useLayoutEffect)(function(){ue||r({inputPrimaryColor:ye})},[]),(0,g.useEffect)(function(){return document.addEventListener("gform/dialog/confirm",Z),function(){document.removeEventListener("gform/dialog/confirm",Z)}},[]),(0,g.useEffect)(function(){r({formPreview:C}),r({title:j}),r({description:z}),r({ajax:M}),r({defaults:K})},[C,j,z,M,K]);var W=function(e){return J.data.forms.find(function(t){return t.id==e})},X=function(){for(var e=[{label:Q.select_a_form,value:""}],t=0;t<J.data.forms.length;t++){var r=J.data.forms[t];e.push({label:r.title,value:r.id})}return e},Y=function(e,t){e.preventDefault();var r=G(J.data.adminURL,t);window.open(r,"_blank","noopener")},Z=function(e){var t;if("gform/dialog/confirm"===e.type&&(null==e||null===(t=e.detail)||void 0===t||null===(t=t.instance)||void 0===t||null===(t=t.options)||void 0===t?void 0:t.id)==="restore-default-settings-dialog-"+a){var n=V(V({},J.data.styles.defaults),{},{theme:"orbital"});J.data.styles.defaults.inputPrimaryColor||(n.inputPrimaryColor=n.buttonPrimaryBackgroundColor),r(n)}},ee=function(e){var t=document.getElementById(e);if(t){var r=t.closest(".gform-dialog__mask");r&&r.remove()}},te=function(){if(!a)return null;var e={page:"gf_edit_forms",id:a},t={page:"gf_edit_forms",id:a,view:"settings"},r={key:"gform-block-custom-controls"},n={key:"gform-block-edit-form-buttton",onClick:function(t){Y(t,e)},className:(0,p.classnames)({"gform-block__toolbar-button":!0})},o={text:Q.edit_form},l={className:(0,p.classnames)({"gform-icon":!0,"gform-icon--create":!0})},i={key:"gform-block-form-settings-button",onClick:function(e){Y(e,t)},className:(0,p.classnames)({"gform-block__toolbar-button":!0})},c={text:Q.form_settings},s={className:(0,p.classnames)({"gform-icon":!0,"gform-icon--cog":!0})};return React.createElement(m.BlockControls,r,React.createElement(u.ToolbarButton,n,React.createElement(u.Tooltip,o,React.createElement("i",l))),React.createElement(u.ToolbarButton,i,React.createElement(u.Tooltip,c,React.createElement("i",s))))},re=function(){if(document.addEventListener("gform/dialog/confirm",Z),!J.data.forms||J.data.forms.length<=0)return null;var e=J.data.styles.defaults,t=(J.data.orbitalDefault,{key:"inspector"}),n={title:Q.form_settings},o={label:Q.form,value:a,options:X(),onChange:function(e){return d(e)},__nextHasNoMarginBottom:!0},l={label:Q.show_form_title,checked:j,onChange:function(){return B(!j)},__nextHasNoMarginBottom:!0},i={label:Q.show_form_description,checked:z,onChange:function(){return I(!z)},__nextHasNoMarginBottom:!0},c={title:Q.form_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0,"gform-block__form-styles":!0})},f=J.data.styles.defaults.theme.replace("-theme","");"Gravity"===(f=f.charAt(0).toUpperCase()+f.slice(1))&&(f="Gravity Forms 2.5");var b={label:Q.form_theme,value:ae,options:[{label:(0,s.sprintf)(Q.inherit_from_default,f),value:""},{label:Q.gravity_forms_25_theme,value:"gravity-theme"},{label:Q.orbital_theme,value:"orbital"}],className:(0,p.classnames)({"gform-block__theme-selector":!0}),onChange:function(e){return r({theme:e})},__nextHasNoMarginBottom:!0},y={variant:"primary",text:Q.reset_defaults,size:"full-width",className:(0,p.classnames)({"gform-block__theme-reset-defaults":!0}),onClick:function(e){!function(e,t){var r="restore-default-settings-dialog-"+t;new(h())({confirmButtonText:Q.restore_defaults,content:Q.this_will_restore_defaults,cancelButtonText:Q.cancel,closeButtonTitle:Q.close,id:r,maskBlur:!1,maskTheme:"none",mode:"dialog",onClose:function(){ee(r)},title:Q.restore_default_styles,titleIconColor:"#DD301D",zIndex:100055}).showDialog()}(0,a)}},v={className:(0,p.classnames)({"gform-alert":!0,"gform-alert--notice":!0,"gform-alert--theme-primary":!0,"gform-alert--inline":!0})},w={ariaHidden:!0,className:(0,p.classnames)({"gform-alert__icon":!0,"gform-icon":!0,"gform-icon--circle-notice-fine":!0})},k={className:(0,p.classnames)({"gform-alert__message-wrap":!0})},x={className:(0,p.classnames)({"gform-alert__message":!0}),dangerouslySetInnerHTML:{__html:(0,s.sprintf)(Q.form_style_options_not_available,'<a target="_blank" href="'.concat(J.data.adminURL,"?page=gf_edit_forms&view=settings&subview=settings&id=").concat(a,'">'),'<span class="screen-reader-text">'.concat((0,_.__)("(opens in a new tab)","gravityforms"),'</span>&nbsp;<span class="gform-icon gform-icon--external-link"></span></a>'))}},E={title:Q.input_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},N={align:"flex-start"},S={className:(0,p.classnames)({"gform-block__components-base-control--adjust-label-line-height":!0}),label:Q.size,value:le,options:[{label:Q.small,value:"sm"},{label:Q.medium,value:"md"},{label:Q.large,value:"lg"}],onChange:function(e){return r({inputSize:e})},__nextHasNoMarginBottom:!0},P={className:(0,p.classnames)({"gform-block__components-base-control--adjust-label-line-height":!0}),label:Q.border_radius,help:Q.in_pixels,value:ie,type:"number",onChange:function(e){return r({inputBorderRadius:e})},__nextHasNoMarginBottom:!0},O={level:3},T={label:Q.background,color:se,controlled:!0,defaultColor:e.inputBackgroundColor,onChange:function(e){return r({inputBackgroundColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__first-child-palette":!0})},F={label:Q.border,color:ce,controlled:!0,defaultColor:e.inputBorderColor,onChange:function(e){return r({inputBorderColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__middle-child-palette":!0})},L={label:Q.text,color:me,controlled:!0,defaultColor:e.inputColor,onChange:function(e){return r({inputColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__middle-child-palette":!0})},H={label:Q.accent,color:ue,controlled:!0,defaultColor:e.inputPrimaryColor,onChange:function(e){r(e?{inputPrimaryColor:e}:{inputPrimaryColor:ye})},className:(0,p.classnames)({"gform-block-editor-panel__last-child-palette":!0})},V={marginTop:2},G={variant:"muted",size:"subheadline"},K={backgroundColor:se,textColor:me},Y={backgroundColor:se,textColor:ue},te={title:Q.image_choice_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},re={align:"flex-start"},we={className:(0,p.classnames)({"gform-block__components-base-control--adjust-label-line-height":!0}),label:Q.appearance,value:de,options:[{label:Q.card,value:"card"},{label:Q.no_card,value:"no-card"}],onChange:function(e){return r({inputImageChoiceAppearance:e})},__nextHasNoMarginBottom:!0},ke={className:(0,p.classnames)({"gform-block__components-base-control--adjust-label-line-height":!0}),label:Q.style,value:fe,options:[{label:Q.circle,value:"circle"},{label:Q.square,value:"square"}],onChange:function(e){return r({inputImageChoiceStyle:e})},__nextHasNoMarginBottom:!0},xe={className:(0,p.classnames)({"gform-block__components-base-control--adjust-label-line-height":!0}),label:Q.size,value:pe,options:[{label:Q.small,value:"sm"},{label:Q.medium,value:"md"},{label:Q.large,value:"lg"}],onChange:function(e){return r({inputImageChoiceSize:e})},__nextHasNoMarginBottom:!0},Ee={marginTop:2},Ce={title:Q.label_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},Se={label:Q.font_size,help:Q.in_pixels,value:ge,type:"number",onChange:function(e){return r({labelFontSize:e})},__nextHasNoMarginBottom:!0},je={level:3},Be={label:Q.text,color:be,controlled:!0,defaultColor:e.labelColor,onChange:function(e){return r({labelColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__first-child-palette":!0,"gform-block-editor-panel__last-child-palette":!0})},Pe={title:Q.description_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},Oe={label:Q.font_size,help:Q.in_pixels,value:he,type:"number",onChange:function(e){return r({descriptionFontSize:e})},__nextHasNoMarginBottom:!0},ze={level:3},Ie={label:Q.text,color:_e,controlled:!0,defaultColor:e.descriptionColor,onChange:function(e){return r({descriptionColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__first-child-palette":!0,"gform-block-editor-panel__last-child-palette":!0})},Te={title:Q.button_styles,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},Fe={level:3},Me={label:Q.background,color:ye,controlled:!0,defaultColor:e.buttonPrimaryBackgroundColor,onChange:function(e){return r({buttonPrimaryBackgroundColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__first-child-palette":!0})},Ae={label:Q.text,color:ve,controlled:!0,defaultColor:e.buttonPrimaryColor,onChange:function(e){return r({buttonPrimaryColor:e})},className:(0,p.classnames)({"gform-block-editor-panel__last-child-palette":!0})},Le={marginTop:2},De={variant:"muted",size:"subheadline"},He={backgroundColor:ye,textColor:ve},Ve={title:Q.advanced,initialOpen:!0,className:(0,p.classnames)({"gform-block__panel":!0})},Ge={label:Q.preview,checked:C,onChange:function(){return R(!C)},__nextHasNoMarginBottom:!0},Ue={label:Q.ajax,checked:M,onChange:function(){return A(!M)},__nextHasNoMarginBottom:!0},qe={label:Q.field_values,value:oe,onChange:function(e){return r({fieldValues:e})},__nextHasNoMarginBottom:!0},$e={className:(0,p.classnames)({"gform-block__tabindex":!0}),label:Q.tabindex,type:"number",value:ne,onChange:function(e){return r({tabindex:e})},placeholder:"0",__nextHasNoMarginBottom:!0};return React.createElement(m.InspectorControls,t,React.createElement(u.PanelBody,n,React.createElement(u.SelectControl,o),W(a)&&React.createElement(g.Fragment,null,React.createElement(u.ToggleControl,l),React.createElement(u.ToggleControl,i))),React.createElement(u.PanelBody,c,W(a)&&!W(a).isLegacyMarkup&&React.createElement(u.SelectControl,b),W(a)&&!W(a).isLegacyMarkup&&Re&&React.createElement(u.Button,y),W(a)&&!W(a).isLegacyMarkup&&Re&&React.createElement($,V,React.createElement(q,G,React.createElement(u.ExternalLink,{href:"https://docs.gravityforms.com/block-themes-and-style-settings/"},Q.learn_more_orbital))),W(a)&&W(a).isLegacyMarkup&&React.createElement("div",v,React.createElement("span",w),React.createElement("div",k,React.createElement("p",x)))),Ne&&React.createElement(u.PanelBody,E,React.createElement(u.Flex,N,React.createElement(u.FlexBlock,null,React.createElement(u.SelectControl,S)),React.createElement(u.FlexBlock,null,React.createElement(u.TextControl,P))),React.createElement(U,O,Q.colors),React.createElement(D,T),React.createElement(D,F),React.createElement(D,L),React.createElement(D,H),React.createElement($,V,React.createElement(q,G,Q.the_accent_color_is_used)),React.createElement($,V,React.createElement(m.ContrastChecker,K)),React.createElement($,V,React.createElement(m.ContrastChecker,Y))),Ne&&W(a).hasImageChoices&&React.createElement(u.PanelBody,te,React.createElement(u.Flex,re,React.createElement(u.FlexBlock,null,React.createElement(u.SelectControl,we)),React.createElement(u.FlexBlock,null,React.createElement(u.SelectControl,ke))),React.createElement($,Ee,React.createElement(u.SelectControl,xe))),Ne&&React.createElement(u.PanelBody,Ce,React.createElement(u.TextControl,Se),React.createElement(U,je,Q.colors),React.createElement(D,Be)),Ne&&React.createElement(u.PanelBody,Pe,React.createElement(u.TextControl,Oe),React.createElement(U,ze,Q.colors),React.createElement(D,Ie)),Ne&&React.createElement(u.PanelBody,Te,React.createElement(U,Fe,Q.colors),React.createElement(D,Me),React.createElement(D,Ae),React.createElement($,Le,React.createElement(q,De,Q.the_background_color_is_used)),React.createElement($,Le,React.createElement(m.ContrastChecker,He))),a&&React.createElement(u.PanelBody,Ve,W(a)&&React.createElement(u.ToggleControl,Ge),React.createElement(u.ToggleControl,Ue),React.createElement(u.TextareaControl,qe),React.createElement(u.TextControl,$e),React.createElement(g.Fragment,null,(0,s.sprintf)(Q.form_id,a))))},ne=t.tabindex,oe=t.fieldValues,ae=(t.imgPreview,t.theme),le=t.inputSize,ie=t.inputBorderRadius,ce=t.inputBorderColor,se=t.inputBackgroundColor,me=t.inputColor,ue=t.inputPrimaryColor,de=t.inputImageChoiceAppearance,fe=t.inputImageChoiceStyle,pe=t.inputImageChoiceSize,ge=t.labelFontSize,be=t.labelColor,he=t.descriptionFontSize,_e=t.descriptionColor,ye=t.buttonPrimaryBackgroundColor,ve=t.buttonPrimaryColor,we={className:(0,p.classnames)({"gform-block__alert":!0,"gform-block__alert-error":!0})},ke={key:"placeholder",className:(0,p.classnames)({"wp-block-embed":!0,"gform-block__placeholder":!0})},xe={className:(0,p.classnames)({"gform-block__placeholder-brand":!0})},Ee={className:(0,p.classnames)({"gform-icon":!0})},Ce={value:a,onChange:function(e){return d(e.target.value)}},Re="orbital"===ae||J.data.orbitalDefault&&""===ae,Ne=Re&&W(a)&&!W(a).isLegacyMarkup;if(!t.formId||!C)return React.createElement("div",(0,m.useBlockProps)(),te(),re(),w&&React.createElement("div",we,React.createElement("p",null,Q.the_selected_form_deleted)),React.createElement(u.Placeholder,ke,React.createElement("div",xe,React.createElement("div",Ee,y),React.createElement("p",null,React.createElement("strong",null,Q.gravity_forms))),J.data.forms&&J.data.forms.length>0&&React.createElement("form",null,React.createElement("select",Ce,X().map(function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)}))),(!J.data.forms||J.data.forms&&0===J.data.forms.length)&&React.createElement("form",null,React.createElement("p",null,Q.you_must_have_one_form))));var Se=Object.keys(t).filter(function(e){return!J.data.styles.defaults[e]||t[e]!=J.data.styles.defaults[e]}).reduce(function(e,r){return(0,l.A)(e,(0,i.A)({},r,t[r]))},{}),je={key:"form_preview",block:"gravityforms/form",attributes:V(V({},(0,l.A)({},J.data.styles.globals,Se)),{},{theme:t.theme||J.data.styles.defaults.theme})};return React.createElement("div",(0,m.useBlockProps)(),te(),re(),u.ServerSideRender?React.createElement(u.ServerSideRender,je):React.createElement(f(),je))},W=wp.blocks.registerBlockType,X=(null===a()||void 0===a()?void 0:a().block_editor["gravityforms/form"])||{},Y=X.i18n,Z=function(){var e;(0,s.consoleInfo)("Gravity Forms Admin: Initialized form block."),W("gravityforms/form",{title:Y.form,description:Y.select_and_display_form,category:"embed",supports:{customClassName:!1,className:!1,html:!1},keywords:["gravity forms","form","newsletter","contact"],example:{attributes:{imgPreview:!0}},apiVersion:2,attributes:(null==X||null===(e=X.data)||void 0===e?void 0:e.attributes)||{},icon:y,transforms:{from:[{type:"shortcode",tag:["gravityform","gravityforms"],attributes:{formId:{type:"string",shortcode:function(e){var t=e.named.id;return parseInt(t).toString()}},title:{type:"boolean",shortcode:function(e){return"true"===e.named.title}},description:{type:"boolean",shortcode:function(e){return"true"===e.named.description}},ajax:{type:"boolean",shortcode:function(e){return"true"===e.named.ajax}},tabindex:{type:"string",shortcode:function(e){var t=e.named.tabindex;return isNaN(t)?null:parseInt(t).toString()}}}}]},edit:K,save:function(){return null}})}},4749:function(e,t,r){"use strict";var n=r(5798),o=r(1873),a=r(7113),l=function(){function e(t){(0,o.A)(this,e),this.currency=t}return(0,a.A)(e,[{key:"toNumber",value:function(t){return e.isNumeric(t)?parseFloat(t):e.cleanNumber(t,this.currency.symbol_right,this.currency.symbol_left,this.currency.decimal_separator)}},{key:"toMoney",value:function(t){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]||(t=e.cleanNumber(t,this.currency.symbol_right,this.currency.symbol_left,this.currency.decimal_separator)),!1===t)return"";var r="";"-"===(t+="")[0]&&(t=parseFloat(t.substr(1)),r="-");var n=this.numberFormat(t,this.currency.decimals,this.currency.decimal_separator,this.currency.thousand_separator);"0.00"===n&&(r="");var o=this.currency.symbol_left?this.currency.symbol_left+this.currency.symbol_padding:"",a=this.currency.symbol_right?this.currency.symbol_padding+this.currency.symbol_right:"";return n=r+e.htmlDecode(o)+n+e.htmlDecode(a)}},{key:"getCode",value:function(){return"code"in this.currency&&""!==this.currency.code&&this.currency.code}},{key:"numberFormat",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:",",o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e=(e+"").replace(",","").replace(" ","");var a,l,i,c=isFinite(+e)?+e:0,s=isFinite(+t)?Math.abs(t):0,m="";return 0===parseInt(t)?(c+=1e-10,m=(""+Math.round(c)).split(".")):m=-1===parseInt(t)?(""+c).split("."):(a=c+=1e-10,l=s,i=Math.pow(10,l),""+Math.round(a*i)/i).split("."),m[0].length>3&&(m[0]=m[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,n)),o&&(m[1]||"").length<s&&(m[1]=m[1]||"",m[1]+=new Array(s-m[1].length+1).join("0")),m.join(r)}}],[{key:"cleanNumber",value:function(t,r,n,o){var a="",l="",i="",c=!1;t=(t=(t=(t+=" ").replace(/&.*?;/g,"")).replace(r,"")).replace(n,"");for(var s=0;s<t.length;s++)i=t.substr(s,1),parseInt(i,10)>=0&&parseInt(i,10)<=9||i===o?a+=i:"-"===i&&(c=!0);for(var m=0;m<a.length;m++)(i=a.substr(m,1))>="0"&&i<="9"?l+=i:i===o&&(l+=".");return c&&(l="-"+l),!!e.isNumeric(l)&&parseFloat(l)}},{key:"isNumeric",value:function(e){return(0,n.isNumber)(e)}},{key:"getDecimalSeparator",value:function(e){var t;switch(e){case"currency":t=window.gf_global.gf_currency_config.decimal_separator;break;case"decimal_comma":t=",";break;default:t="."}return t}},{key:"htmlDecode",value:function(e){var t,r,n=e,o=n.match(/&#[0-9]{1,5};/g);if(null!=o)for(var a=0;a<o.length;a++)n=(t=(r=o[a]).substring(2,r.length-1))>=-32768&&t<=65535?n.replace(r,String.fromCharCode(t)):n.replace(r,"");return n}}])}();window.gform=window.gform||{},window.gform.Currency=l;var i,c=function(){(0,n.consoleInfo)("Gravity Forms Common: Initialized all javascript that targeted document ready.")},s=function(){(0,n.ready)(c)},m=function(){s()},u=r(428),d=r.n(u),f=r(9662),p=r.n(f),g=r(1533),b=r.n(g),h=r(5973),_=r.n(h),y={containers:(0,n.getNodes)("page-loader",!0)},v={rendered:!1},w=(null===b()||void 0===b()||null===(i=b().form_settings)||void 0===i?void 0:i.loader)||{},k=function(){p().instances.loaders.pageLoader.hideLoader()},x=function(){v.rendered?p().instances.loaders.pageLoader.showLoader():(p().instances.loaders.pageLoader.init(),v.rendered=!0)},E=function(){var e;p().instances=p().instances||{},p().instances.loaders=p().instances.loaders||{},e=w.i18n.loaderText,p().instances.loaders.pageLoader=new(_())({id:"gform-page-loader",position:"sticky",renderOnInit:!1,target:"#wpbody-content",text:(0,n.escapeHtml)(e)}),y.containers.forEach(function(e){"form"===e.tagName.toLowerCase()&&d()(e).on("submit",x)}),document.addEventListener("gform/page_loader/show",x),document.addEventListener("gform/page_loader/hide",k),(0,n.consoleInfo)("Gravity Forms Admin: Initialized page loader.")},C=r(4936),R=function(e){(0,C.Ay)(e.detail)},N=function(){document.addEventListener("gform/snackbar/render",R),(0,n.consoleInfo)("Gravity Forms Admin: Initialized snackbar component.")},S={editorButton:(0,n.getNode)("editor-flyout-trigger"),embedForm:(0,n.getNode)("embed-flyout-trigger"),taggable:(0,n.getNode)(".merge-tag-support",document,!0),postSelect:(0,n.getNodes)("gform-settings-field-select",!0),userSelect:(0,n.getNodes)("gform-settings-field-user-select",!0),formSwitcher:(0,n.getNodes)("gform-form-switcher",!0)},j=function(){E(),N(),S.editorButton&&Promise.all([r.e(187),r.e(957)]).then(r.bind(r,2623)).then(function(e){e.default()}),S.embedForm&&Promise.all([r.e(187),r.e(129)]).then(r.bind(r,5674)).then(function(e){e.default()}),S.taggable&&Promise.all([r.e(187),r.e(940),r.e(781)]).then(r.bind(r,5194)).then(function(e){e.default()}),S.postSelect.length&&r.e(610).then(r.bind(r,3865)).then(function(e){e.default(S.postSelect)}),S.userSelect.length&&r.e(631).then(r.bind(r,4060)).then(function(e){e.default(S.userSelect)}),S.formSwitcher.length&&r.e(355).then(r.bind(r,4832)).then(function(e){e.default(S.formSwitcher)}),(0,n.consoleInfo)("Gravity Forms Admin: Initialized all admin components.")},B=r(9819),P={formEditor:(0,n.getNodes)("form-editor-wrapper")[0],formSettings:(0,n.getNodes)("form-settings")[0],splashPageModal:(0,n.getNodes)("gf-splash-template")[0],systemReportButton:(0,n.getNodes)("gf-copy-system-report")[0]},O=function(){b().data.is_block_editor&&r.e(832).then(r.bind(r,1026)).then(function(e){e.default()}),P.formEditor&&Promise.all([r.e(187),r.e(281),r.e(301)]).then(r.bind(r,5248)).then(function(e){e.default(P.formEditor)}),!P.formEditor&&(0,n.shouldLoadChunk)("form-saver")&&Promise.all([r.e(187),r.e(281),r.e(10)]).then(r.bind(r,8015)).then(function(e){e.default()}),P.splashPageModal&&r.e(318).then(r.bind(r,7363)).then(function(e){e.default(P.splashPageModal)}),P.systemReportButton&&r.e(959).then(r.bind(r,9712)).then(function(e){e.default(P.systemReportButton)})},z=function(){window.gform.initializeFieldMap=function(e,t){Promise.all([r.e(187),r.e(940),r.e(392)]).then(r.bind(r,6279)).then(function(r){window.wp.element.createRoot(document.getElementById(e)).render(React.createElement(r.FieldMap,t))})},window.initializeFieldMap=window.gform.initializeFieldMap},I=function(){m(),j(),z(),(0,B.default)(),O(),(0,n.trigger)({event:"gform/admin/scripts_loaded"}),(0,n.consoleInfo)("Gravity Forms Admin: Initialized all javascript that targeted document ready.")},T=function(){(0,n.ready)(I)};(b().hmr_dev||(r.p=b().public_path),b().data.is_block_editor)&&r(4504).default();T()},5695:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.StatusIndicator},5704:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Dialog},5798:function(e){"use strict";e.exports=window.gform.utils},5973:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.Loader},6087:function(e){"use strict";e.exports=window.wp.element},6243:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Icon},6648:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Heading},6900:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.Button},7038:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Tag},7122:function(e){"use strict";e.exports=ajaxurl},7143:function(e){"use strict";e.exports=window.wp.data},7195:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.Dropdown},7510:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Input},7616:function(e){"use strict";e.exports=window.gform.libraries},8045:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Cards.FormTemplateCard},8150:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.Input},8200:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Toggle},8264:function(e){"use strict";e.exports=window.gform.components.admin.html.elements.Toggle},8332:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.Videos.VidyardVideo},8335:function(e){"use strict";e.exports=window.gform.utils.react},9280:function(e){"use strict";e.exports=window.regeneratorRuntime},9328:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.List},9652:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Grid},9662:function(e){"use strict";e.exports=gform},9819:function(e,t,r){"use strict";r.r(t);var n=r(1533),o=r.n(n),a=r(5798),l=(null===o()||void 0===o()?void 0:o().apps)||{};t.default=function(){var e=function(e){var t=l[e];if(!t.should_display)return 1;r(379)("".concat(t.chunk_path)).then(function(r){(0,a.trigger)({event:"gform/apps/before_load/".concat(e),el:document,data:{app:t},native:!1}),r.default(),(0,a.trigger)({event:"gform/apps/after_load/".concat(e),el:document,data:{app:t},native:!1})})};for(var t in l)e(t);(0,a.consoleInfo)("Gravity Forms Admin: Initialized all apps.")}},9877:function(e){"use strict";e.exports=window.gform.components.admin.react.elements.Box},9914:function(e){"use strict";e.exports=window.gform.components.admin.react.modules.NavBar}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}};return n[e].call(r.exports,r,r.exports,a),r.exports}a.m=n,e=[],a.O=function(t,r,n,o){if(!r){var l=1/0;for(m=0;m<e.length;m++){r=e[m][0],n=e[m][1],o=e[m][2];for(var i=!0,c=0;c<r.length;c++)(!1&o||l>=o)&&Object.keys(a.O).every(function(e){return a.O[e](r[c])})?r.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(m--,1);var s=n();void 0!==s&&(t=s)}}return t}o=o||0;for(var m=e.length;m>0&&e[m-1][2]>o;m--)e[m]=e[m-1];e[m]=[r,n,o]},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce(function(t,r){return a.f[r](e,t),t},[]))},a.u=function(e){return({10:"scripts-admin.form-ajax-save",100:"scripts-admin.setup-wizard-Screen03",129:"scripts-admin.embed-form",215:"scripts-admin.template-library-utils",301:"scripts-admin.form-editor",318:"scripts-admin.splash-page",355:"scripts-admin.form-switcher",392:"scripts-admin.field-map",441:"scripts-admin.template-library-template-library-grid",528:"scripts-admin.template-library-template-library",610:"scripts-admin.post-select",631:"scripts-admin.user-select",647:"scripts-admin.template-library",694:"scripts-admin.setup-wizard-setup-wizard",707:"scripts-admin.setup-wizard-Screen04",752:"scripts-admin.template-library-template-library-flyout",781:"scripts-admin.merge-tags",832:"scripts-admin.block-editor",854:"scripts-admin.setup-wizard-store",874:"scripts-admin.setup-wizard",898:"scripts-admin.setup-wizard-Screen05",907:"scripts-admin.template-library-store",909:"scripts-admin.setup-wizard-Screen02",910:"scripts-admin.setup-wizard-Screen01",957:"scripts-admin.editor-button",959:"scripts-admin.system-report"}[e]||e)+"."+{10:"a5cf9ea261f2b70e0d34",100:"88fbc929978a341adc9b",129:"4236cee15568a6aeabee",215:"f5d6b071d3c89358e918",281:"6c84c9b36432e4b846a6",301:"a2502657d6a78ef31406",318:"1affeee3abd5ad39ad0f",355:"f366e9284be7f89ac7bd",392:"5583b15a6064c31b4195",441:"7730d76783bbe5e254aa",528:"55738931d242ed1d2ef0",610:"523351094a26023264cd",631:"b5234c9e82c3e8f482cd",647:"6f768295f7f2bac01a0b",694:"23982eb94b80a340643c",707:"7901a6644bdabfc16c18",752:"cf684c1ac27314683ce1",781:"bfcc289582f6c881de4c",832:"33e6124c6b75ecbe85bd",854:"e394030a6b28b56ba99c",874:"f7c82893e3778a1a9b52",898:"8a95d545ffbd1422e294",907:"d4981e3023ec863c1120",909:"8fb2366cdc716cbea685",910:"910e55ec0cdbfe32f5d9",940:"f758fe339271b4dc5c87",957:"7d87807aa7c0bd539b97",959:"b3bcb90c0e5ea759efd6"}[e]+".min.js"},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t={},r="gravityforms:",a.l=function(e,n,o,l){if(t[e])t[e].push(n);else{var i,c;if(void 0!==o)for(var s=document.getElementsByTagName("script"),m=0;m<s.length;m++){var u=s[m];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==r+o){i=u;break}}i||(c=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,a.nc&&i.setAttribute("nonce",a.nc),i.setAttribute("data-webpack",r+o),i.src=e),t[e]=[n];var d=function(r,n){i.onerror=i.onload=null,clearTimeout(f);var o=t[e];if(delete t[e],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach(function(e){return e(n)}),r)return r(n)},f=setTimeout(d.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=d.bind(null,i.onerror),i.onload=d.bind(null,i.onload),c&&document.head.appendChild(i)}},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e}(),function(){var e={699:0};a.f.j=function(t,r){var n=a.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(function(r,o){n=e[t]=[r,o]});r.push(n[2]=o);var l=a.p+a.u(t),i=new Error;a.l(l,function(r){if(a.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+l+")",i.name="ChunkLoadError",i.type=o,i.request=l,n[1](i)}},"chunk-"+t,t)}},a.O.j=function(t){return 0===e[t]};var t=function(t,r){var n,o,l=r[0],i=r[1],c=r[2],s=0;if(l.some(function(t){return 0!==e[t]})){for(n in i)a.o(i,n)&&(a.m[n]=i[n]);if(c)var m=c(a)}for(t&&t(r);s<l.length;s++)o=l[s],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(m)},r=self.webpackChunkgravityforms=self.webpackChunkgravityforms||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}(),a.O(void 0,[187],function(){return a(7920)});var l=a.O(void 0,[187],function(){return a(4749)});l=a.O(l)}();