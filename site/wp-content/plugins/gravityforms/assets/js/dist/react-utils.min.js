!function(){var e={956:function(e){"use strict";var t="%[a-f0-9]{2}",r=new RegExp("("+t+")|([^%]+?)","gi"),n=new RegExp("("+t+")+","gi");function o(e,t){try{return[decodeURIComponent(e.join(""))]}catch(e){}if(1===e.length)return e;t=t||1;var r=e.slice(0,t),n=e.slice(t);return Array.prototype.concat.call([],o(r),o(n))}function i(e){try{return decodeURIComponent(e)}catch(i){for(var t=e.match(r)||[],n=1;n<t.length;n++)t=(e=o(t,n).join("")).match(r)||[];return e}}e.exports=function(e){if("string"!=typeof e)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof e+"`");try{return e=e.replace(/\+/g," "),decodeURIComponent(e)}catch(t){return function(e){for(var t={"%FE%FF":"��","%FF%FE":"��"},r=n.exec(e);r;){try{t[r[0]]=decodeURIComponent(r[0])}catch(e){var o=i(r[0]);o!==r[0]&&(t[r[0]]=o)}r=n.exec(e)}t["%C2"]="�";for(var c=Object.keys(t),u=0;u<c.length;u++){var a=c[u];e=e.replace(new RegExp(a,"g"),t[a])}return e}(e)}}},913:function(e){"use strict";e.exports=function(e,t){for(var r={},n=Object.keys(e),o=Array.isArray(t),i=0;i<n.length;i++){var c=n[i],u=e[c];(o?-1!==t.indexOf(c):t(c,u,e))&&(r[c]=u)}return r}},985:function(e,t,r){"use strict";const n=r(182),o=r(956),i=r(646),c=r(913),u=Symbol("encodeFragmentIdentifier");function a(e){if("string"!=typeof e||1!==e.length)throw new TypeError("arrayFormatSeparator must be single character string")}function s(e,t){return t.encode?t.strict?n(e):encodeURIComponent(e):e}function f(e,t){return t.decode?o(e):e}function l(e){return Array.isArray(e)?e.sort():"object"==typeof e?l(Object.keys(e)).sort(((e,t)=>Number(e)-Number(t))).map((t=>e[t])):e}function p(e){const t=e.indexOf("#");return-1!==t&&(e=e.slice(0,t)),e}function d(e){const t=(e=p(e)).indexOf("?");return-1===t?"":e.slice(t+1)}function v(e,t){return t.parseNumbers&&!Number.isNaN(Number(e))&&"string"==typeof e&&""!==e.trim()?e=Number(e):!t.parseBooleans||null===e||"true"!==e.toLowerCase()&&"false"!==e.toLowerCase()||(e="true"===e.toLowerCase()),e}function y(e,t){a((t=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},t)).arrayFormatSeparator);const r=function(e){let t;switch(e.arrayFormat){case"index":return(e,r,n)=>{t=/\[(\d*)\]$/.exec(e),e=e.replace(/\[\d*\]$/,""),t?(void 0===n[e]&&(n[e]={}),n[e][t[1]]=r):n[e]=r};case"bracket":return(e,r,n)=>{t=/(\[\])$/.exec(e),e=e.replace(/\[\]$/,""),t?void 0!==n[e]?n[e]=[].concat(n[e],r):n[e]=[r]:n[e]=r};case"comma":case"separator":return(t,r,n)=>{const o="string"==typeof r&&r.includes(e.arrayFormatSeparator),i="string"==typeof r&&!o&&f(r,e).includes(e.arrayFormatSeparator);r=i?f(r,e):r;const c=o||i?r.split(e.arrayFormatSeparator).map((t=>f(t,e))):null===r?r:f(r,e);n[t]=c};case"bracket-separator":return(t,r,n)=>{const o=/(\[\])$/.test(t);if(t=t.replace(/\[\]$/,""),!o)return void(n[t]=r?f(r,e):r);const i=null===r?[]:r.split(e.arrayFormatSeparator).map((t=>f(t,e)));void 0!==n[t]?n[t]=[].concat(n[t],i):n[t]=i};default:return(e,t,r)=>{void 0!==r[e]?r[e]=[].concat(r[e],t):r[e]=t}}}(t),n=Object.create(null);if("string"!=typeof e)return n;if(!(e=e.trim().replace(/^[?#&]/,"")))return n;for(const o of e.split("&")){if(""===o)continue;let[e,c]=i(t.decode?o.replace(/\+/g," "):o,"=");c=void 0===c?null:["comma","separator","bracket-separator"].includes(t.arrayFormat)?c:f(c,t),r(f(e,t),c,n)}for(const e of Object.keys(n)){const r=n[e];if("object"==typeof r&&null!==r)for(const e of Object.keys(r))r[e]=v(r[e],t);else n[e]=v(r,t)}return!1===t.sort?n:(!0===t.sort?Object.keys(n).sort():Object.keys(n).sort(t.sort)).reduce(((e,t)=>{const r=n[t];return Boolean(r)&&"object"==typeof r&&!Array.isArray(r)?e[t]=l(r):e[t]=r,e}),Object.create(null))}t.extract=d,t.parse=y,t.stringify=(e,t)=>{if(!e)return"";a((t=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},t)).arrayFormatSeparator);const r=r=>t.skipNull&&null==e[r]||t.skipEmptyString&&""===e[r],n=function(e){switch(e.arrayFormat){case"index":return t=>(r,n)=>{const o=r.length;return void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,[s(t,e),"[",o,"]"].join("")]:[...r,[s(t,e),"[",s(o,e),"]=",s(n,e)].join("")]};case"bracket":return t=>(r,n)=>void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,[s(t,e),"[]"].join("")]:[...r,[s(t,e),"[]=",s(n,e)].join("")];case"comma":case"separator":case"bracket-separator":{const t="bracket-separator"===e.arrayFormat?"[]=":"=";return r=>(n,o)=>void 0===o||e.skipNull&&null===o||e.skipEmptyString&&""===o?n:(o=null===o?"":o,0===n.length?[[s(r,e),t,s(o,e)].join("")]:[[n,s(o,e)].join(e.arrayFormatSeparator)])}default:return t=>(r,n)=>void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,s(t,e)]:[...r,[s(t,e),"=",s(n,e)].join("")]}}(t),o={};for(const t of Object.keys(e))r(t)||(o[t]=e[t]);const i=Object.keys(o);return!1!==t.sort&&i.sort(t.sort),i.map((r=>{const o=e[r];return void 0===o?"":null===o?s(r,t):Array.isArray(o)?0===o.length&&"bracket-separator"===t.arrayFormat?s(r,t)+"[]":o.reduce(n(r),[]).join("&"):s(r,t)+"="+s(o,t)})).filter((e=>e.length>0)).join("&")},t.parseUrl=(e,t)=>{t=Object.assign({decode:!0},t);const[r,n]=i(e,"#");return Object.assign({url:r.split("?")[0]||"",query:y(d(e),t)},t&&t.parseFragmentIdentifier&&n?{fragmentIdentifier:f(n,t)}:{})},t.stringifyUrl=(e,r)=>{r=Object.assign({encode:!0,strict:!0,[u]:!0},r);const n=p(e.url).split("?")[0]||"",o=t.extract(e.url),i=t.parse(o,{sort:!1}),c=Object.assign(i,e.query);let a=t.stringify(c,r);a&&(a=`?${a}`);let f=function(e){let t="";const r=e.indexOf("#");return-1!==r&&(t=e.slice(r)),t}(e.url);return e.fragmentIdentifier&&(f=`#${r[u]?s(e.fragmentIdentifier,r):e.fragmentIdentifier}`),`${n}${a}${f}`},t.pick=(e,r,n)=>{n=Object.assign({parseFragmentIdentifier:!0,[u]:!1},n);const{url:o,query:i,fragmentIdentifier:a}=t.parseUrl(e,n);return t.stringifyUrl({url:o,query:c(i,r),fragmentIdentifier:a},n)},t.exclude=(e,r,n)=>{const o=Array.isArray(r)?e=>!r.includes(e):(e,t)=>!r(e,t);return t.pick(e,o,n)}},646:function(e){"use strict";e.exports=(e,t)=>{if("string"!=typeof e||"string"!=typeof t)throw new TypeError("Expected the arguments to be of type `string`");if(""===t)return[e];const r=e.indexOf(t);return-1===r?[e]:[e.slice(0,r),e.slice(r+t.length)]}},182:function(e){"use strict";e.exports=e=>encodeURIComponent(e).replace(/[!'()*]/g,(e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`))},26:function(e,t,r){var n=r(353);e.exports=function(e){return null==e?"\\s":e.source?e.source:"["+n(e)+"]"}},353:function(e,t,r){var n=r(728);e.exports=function(e){return n(e).replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},661:function(e){e.exports={nbsp:" ",cent:"¢",pound:"£",yen:"¥",euro:"€",copy:"©",reg:"®",lt:"<",gt:">",quot:'"',amp:"&",apos:"'"}},728:function(e){e.exports=function(e){return null==e?"":""+e}},649:function(e,t,r){var n=r(728);e.exports=function(e){return n(e).replace(/<\/?[^>]+>/g,"")}},768:function(e,t,r){var n=r(728),o=r(26),i=String.prototype.trim;e.exports=function(e,t){return e=n(e),!t&&i?i.call(e):(t=o(t),e.replace(new RegExp("^"+t+"+|"+t+"+$","g"),""))}},517:function(e,t,r){var n=r(728),o=r(661);e.exports=function(e){return n(e).replace(/\&([^;]{1,10});/g,(function(e,t){var r;return t in o?o[t]:(r=t.match(/^#x([\da-fA-F]+)$/))?String.fromCharCode(parseInt(r[1],16)):(r=t.match(/^#(\d+)$/))?String.fromCharCode(~~r[1]):e}))}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e={};function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,c,u=[],a=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;a=!1}else for(;!(a=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{if(!a&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(s)throw o}}return u}}(e,t)||n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.r(e),r.d(e,{CacheProvider:function(){return _},ConditionalWrapper:function(){return i},CrossFade:function(){return w},ExternalDataProvider:function(){return B},FadeIn:function(){return S},HermesProvider:function(){return ft},IdProvider:function(){return yt},JSXFromText:function(){return R},MediaManagerProvider:function(){return Et},RouteHandlerProvider:function(){return Lt},SprintR:function(){return k},StoreProvider:function(){return Bt},buildCacheKey:function(){return I},buildQueryString:function(){return Ke},create:function(){return hr},getComponents:function(){return x},useCache:function(){return T},useCacheContext:function(){return L},useExternalDataContext:function(){return W},useFocusTrap:function(){return er},useHermes:function(){return nt},useHermesContext:function(){return st},useHijackWpMenu:function(){return or},useId:function(){return d},useIdContext:function(){return vt},useMediaManager:function(){return gt},useMediaManagerContext:function(){return St},usePopup:function(){return ur},useRegisterMediaManager:function(){return sr},useRouteHandler:function(){return It},useRouteHandlerContext:function(){return Nt},useScript:function(){return mr},useStateWithDep:function(){return jr},useStoreContext:function(){return Wt}});var i=function(e){var t=e.condition,r=e.wrapper,n=e.children;return t?r(n):n};function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},c.apply(null,arguments)}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function a(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}function s(e,t,r){return(t=a(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var f=window.gform.libraries,l=window.gform.utils,p=f.React.useState,d=function(e){return o(p((function(){return e||(0,l.uniqueId)("id")})),1)[0]};function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=f.React.Children,b=f.React.useEffect,g=f.React.useRef,h=f.React.useState,O=function(e){var t=e.activeIndex,r=void 0===t?0:t,n=e.children,i=e.childWrapperAttributes,u=void 0===i?{}:i,a=e.childWrapperClasses,s=void 0===a?[]:a,l=e.customAttributes,p=void 0===l?{}:l,v=e.customClasses,O=void 0===v?[]:v,w=e.duration,j=void 0===w?250:w,P=e.id,S=void 0===P?"":P,E=g([]),A=o(h(r),2),x=A[0],R=A[1],k=o(h(null),2),C=k[0],D=k[1],F=d(S);E.current&&E.current.length===m.count(n)||(E.current=Array.from({length:m.count(n)},(function(){return null}))),b((function(){R(r),setTimeout((function(){return D(r)}),j)}),[r,j]);var I=y(y({className:(0,f.classnames)(O),id:F},p),{},{style:y({position:"relative"},p.style||{})});return f.React.createElement("div",I,m.map(n,(function(e,t){var r=[x,C].includes(t),n="".concat(F,"-").concat(t),o={transition:"opacity ".concat(j,"ms")};t===x?o.opacity=1:t===C&&(o.position="absolute",o.top="0",o.left="0",o.width="100%",o.opacity=0);var i=y({className:(0,f.classnames)(s),id:n,style:o},u);return f.React.createElement("div",c({},i,{key:n,ref:function(e){return E.current[t]=e}}),r&&e)})))};O.propTypes={activeIndex:f.PropTypes.number,children:f.PropTypes.node,childWrapperAttributes:f.PropTypes.object,childWrapperClasses:f.PropTypes.oneOfType([f.PropTypes.string,f.PropTypes.array,f.PropTypes.object]),customAttributes:f.PropTypes.object,customClasses:f.PropTypes.oneOfType([f.PropTypes.string,f.PropTypes.array,f.PropTypes.object]),duration:f.PropTypes.number,id:f.PropTypes.string};var w=O,j=f.React.useState,P=f.React.useEffect;function S(e){var t=o(j(0),2),r=t[0],n=t[1],i="number"==typeof e.transitionDuration?e.transitionDuration:400,c="number"==typeof e.transformDistance?e.transformDistance:20,u="number"==typeof e.delay?e.delay:50,a=e.wrapperTag||"div",s=e.childTag||"div",l=void 0===e.visible||e.visible;return P((function(){var t=f.React.Children.count(e.children);if(l||(t=0),t===r){var o=setTimeout((function(){e.onComplete&&e.onComplete()}),i);return function(){return clearTimeout(o)}}var c=t>r?1:-1,a=setTimeout((function(){n(r+c)}),u);return function(){return clearTimeout(a)}}),[f.React.Children.count(e.children),u,r,l,i]),f.React.createElement(a,{className:e.className},f.React.Children.map(e.children,(function(t,n){return f.React.createElement(s,{className:e.childClassName,style:{transition:"opacity ".concat(i,"ms, transform ").concat(i,"ms"),transform:r>n?"none":"translateY(".concat(c,"px)"),opacity:r>n?1:0}},t)})))}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function x(e,t){return Object.keys(e).reduce((function(r,n){var o=t[n]||{};return r[n]={component:o.component||e[n],props:A({$style:o.style},o.props)},r}),{})}function R(e){var t,r,n,o,i=e.closingSymbol,u=void 0===i?"%%":i,a=e.openingSymbol,s=void 0===a?"%%":a,l=e.text,p=void 0===l?"":l,d=e.tokens,v=(void 0===d?[]:d).reduce((function(e,t){return e[t.key]=t,e}),{});return f.React.createElement(f.React.Fragment,null,(t=p,r=new RegExp("".concat(s,"(.*?)").concat(u,"([\\s\\S]*?)").concat(s,"\\1").concat(u),"g"),n=0,o=[],t.replace(r,(function(e,r,i,u){o.push(t.slice(n,u));var a=v[r];if(a){var s=a.component;o.push(f.React.createElement(s,c({},a.props,{key:"".concat(r,"-").concat(u)}),i))}else o.push(i);n=u+e.length})),o.push(t.slice(n)),o))}function k(e){var t=e.text,r=void 0===t?"":t,n=e.tokens,o=void 0===n?[]:n;return f.React.createElement(f.React.Fragment,null,r.split(/(%\d\$s)/).map((function(e,t){var r=e.match(/%(\d)\$s/);if(r){var n=parseInt(r[1],10)-1,i=o[n];if(!i)return null;var u=i.component;return f.React.createElement(u,c({key:t},i.props))}return e})))}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var F=f.React.useState,I=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).reduce((function(e,r){return t[r]?"".concat(e,"-").concat(t[r]):e}),e)},T=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=o(F((function(){return e})),2),r=t[0],n=t[1];return{cache:r,addCache:function(e,t){n((function(r){return D(D({},r),{},s({},e,t))}))}}},M=f.React.createContext,q=f.React.useContext,N=M(null),L=function(){return q(N)},_=function(e){var t=e.children,r=e.initialState,n=T(r);return f.React.createElement(N.Provider,{value:n},t)},H=f.React.createContext,$=f.React.useContext,U=H(null),W=function(){var e=$(U);if(!e)throw new Error("useExternalDataContext must be used within an ExternalDataProvider.");return e},B=function(e){var t=e.config,r=e.children,n={config:t,c:K(t),d:K(J(t,"data")),t:K(J(t,"i18n"))};return f.React.createElement(U.Provider,{value:n},r)},J=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return Object.keys(e||{}).reduce((function(r,n){var o;return null!==(o=e[n])&&void 0!==o&&o[t]&&"object"===u(e[n][t])&&(r[n]=e[n][t]),r}),{})},K=function(e){return function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.fallback,i=void 0===o?"":o,c=n.returnObject,a=void 0!==c&&c,s=null==e?void 0:e[t];if(!s)return i;if(a&&(!r||""===r))return s;var f=r?function(e,t){if(e&&t)return t.split(".").reduce((function(e,t){return e&&void 0!==e[t]?e[t]:void 0}),e)}(s,r):void 0;return a&&f&&"object"===u(f)||void 0!==f?f:i}};function z(e,t,r,n,o,i,c){try{var u=e[i](c),a=u.value}catch(e){return void r(e)}u.done?t(a):Promise.resolve(a).then(n,o)}var Q=window.regeneratorRuntime,X=r.n(Q);function Y(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function G(e){return function t(r){return 0===arguments.length||Y(r)?t:e.apply(this,arguments)}}function V(e){return function t(r,n){switch(arguments.length){case 0:return t;case 1:return Y(r)?t:G((function(t){return e(r,t)}));default:return Y(r)&&Y(n)?t:Y(r)?G((function(t){return e(t,n)})):Y(n)?G((function(t){return e(r,t)})):e(r,n)}}}function Z(e){for(var t,r=[];!(t=e.next()).done;)r.push(t.value);return r}function ee(e,t,r){for(var n=0,o=r.length;n<o;){if(e(t,r[n]))return!0;n+=1}return!1}function te(e,t){return Object.prototype.hasOwnProperty.call(t,e)}var re="function"==typeof Object.is?Object.is:function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t},ne=Object.prototype.toString,oe=function(){return"[object Arguments]"===ne.call(arguments)?function(e){return"[object Arguments]"===ne.call(e)}:function(e){return te("callee",e)}}(),ie=oe,ce=!{toString:null}.propertyIsEnumerable("toString"),ue=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],ae=function(){return arguments.propertyIsEnumerable("length")}(),se=function(e,t){for(var r=0;r<e.length;){if(e[r]===t)return!0;r+=1}return!1},fe="function"!=typeof Object.keys||ae?G((function(e){if(Object(e)!==e)return[];var t,r,n=[],o=ae&&ie(e);for(t in e)!te(t,e)||o&&"length"===t||(n[n.length]=t);if(ce)for(r=ue.length-1;r>=0;)te(t=ue[r],e)&&!se(n,t)&&(n[n.length]=t),r-=1;return n})):G((function(e){return Object(e)!==e?[]:Object.keys(e)})),le=G((function(e){return null===e?"Null":void 0===e?"Undefined":Object.prototype.toString.call(e).slice(8,-1)}));function pe(e,t,r,n){var o=Z(e);function i(e,t){return de(e,t,r.slice(),n.slice())}return!ee((function(e,t){return!ee(i,t,e)}),Z(t),o)}function de(e,t,r,n){if(re(e,t))return!0;var o,i,c=le(e);if(c!==le(t))return!1;if(null==e||null==t)return!1;if("function"==typeof e["fantasy-land/equals"]||"function"==typeof t["fantasy-land/equals"])return"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t)&&"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e);if("function"==typeof e.equals||"function"==typeof t.equals)return"function"==typeof e.equals&&e.equals(t)&&"function"==typeof t.equals&&t.equals(e);switch(c){case"Arguments":case"Array":case"Object":if("function"==typeof e.constructor&&"Promise"===(o=e.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return e===t;break;case"Boolean":case"Number":case"String":if(typeof e!=typeof t||!re(e.valueOf(),t.valueOf()))return!1;break;case"Date":if(!re(e.valueOf(),t.valueOf()))return!1;break;case"Error":return e.name===t.name&&e.message===t.message;case"RegExp":if(e.source!==t.source||e.global!==t.global||e.ignoreCase!==t.ignoreCase||e.multiline!==t.multiline||e.sticky!==t.sticky||e.unicode!==t.unicode)return!1}for(var u=r.length-1;u>=0;){if(r[u]===e)return n[u]===t;u-=1}switch(c){case"Map":return e.size===t.size&&pe(e.entries(),t.entries(),r.concat([e]),n.concat([t]));case"Set":return e.size===t.size&&pe(e.values(),t.values(),r.concat([e]),n.concat([t]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var a=fe(e);if(a.length!==fe(t).length)return!1;var s=r.concat([e]),f=n.concat([t]);for(u=a.length-1;u>=0;){var l=a[u];if(!te(l,t)||!de(t[l],e[l],s,f))return!1;u-=1}return!0}var ve=V((function(e,t){return de(e,t,[],[])})),ye=Array.isArray||function(e){return null!=e&&e.length>=0&&"[object Array]"===Object.prototype.toString.call(e)};function me(e,t,r){return function(){if(0===arguments.length)return r();var n=Array.prototype.slice.call(arguments,0),o=n.pop();if(!ye(o)){for(var i=0;i<e.length;){if("function"==typeof o[e[i]])return o[e[i]].apply(o,n);i+=1}if(function(e){return null!=e&&"function"==typeof e["@@transducer/step"]}(o))return t.apply(null,n)(o)}return r.apply(this,arguments)}}var be=function(){return this.xf["@@transducer/init"]()},ge=function(e){return this.xf["@@transducer/result"](e)},he=function(){function e(e,t){this.xf=t,this.n=e,this.i=0}return e.prototype["@@transducer/init"]=be,e.prototype["@@transducer/result"]=ge,e.prototype["@@transducer/step"]=function(e,t){this.i+=1;var r,n=0===this.n?e:this.xf["@@transducer/step"](e,t);return this.n>=0&&this.i>=this.n?(r=n)&&r["@@transducer/reduced"]?r:{"@@transducer/value":r,"@@transducer/reduced":!0}:n},e}(),Oe=V((function(e,t){return new he(e,t)}));function we(e,t){return function(){var r=arguments.length;if(0===r)return t();var n=arguments[r-1];return ye(n)||"function"!=typeof n[e]?t.apply(this,arguments):n[e].apply(n,Array.prototype.slice.call(arguments,0,r-1))}}function je(e){return function t(r,n,o){switch(arguments.length){case 0:return t;case 1:return Y(r)?t:V((function(t,n){return e(r,t,n)}));case 2:return Y(r)&&Y(n)?t:Y(r)?V((function(t,r){return e(t,n,r)})):Y(n)?V((function(t,n){return e(r,t,n)})):G((function(t){return e(r,n,t)}));default:return Y(r)&&Y(n)&&Y(o)?t:Y(r)&&Y(n)?V((function(t,r){return e(t,r,o)})):Y(r)&&Y(o)?V((function(t,r){return e(t,n,r)})):Y(n)&&Y(o)?V((function(t,n){return e(r,t,n)})):Y(r)?G((function(t){return e(t,n,o)})):Y(n)?G((function(t){return e(r,t,o)})):Y(o)?G((function(t){return e(r,n,t)})):e(r,n,o)}}}var Pe=je(we("slice",(function(e,t,r){return Array.prototype.slice.call(r,e,t)}))),Se=V(me(["take"],Oe,(function(e,t){return Pe(0,e<0?1/0:e,t)}))),Ee=V((function(e,t){return ve(Se(e.length,t),e)}));function Ae(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",i=new window.FormData;return function e(t,c){if(!function(e){return Array.isArray(r)&&r.some((function(t){return t===e}))}(c))if(c=c||"",t instanceof window.File)i.append(c,t);else if(Array.isArray(t))for(var a=0;a<t.length;a++)e(t[a],c+"["+a+"]");else if("object"===u(t)&&t)for(var s in t)t.hasOwnProperty(s)&&e(t[s],""===c?s:c+n+s+o);else null!=t&&i.append(c,t)}(e,t),i}r(985);var xe=r(649),Re=r.n(xe),ke=r(768),Ce=r.n(ke),De=r(517),Fe=r.n(De);function Ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Me(e){return qe.apply(this,arguments)}function qe(){var e;return e=X().mark((function e(t){var r,n,o,i,c,u,a,s;return X().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.endpoint,n=void 0===r?"":r,o=t.headers,i=void 0===o?{}:o,c=t.body,u=void 0===c?{}:c,a=t.options,s=Te(Te({},void 0===a?{}:a),{},{method:"POST",headers:Te({},i),body:Ae(u,"",[],"[","]")}),!u.action||"mock_endpoint"!==u.action){e.next=9;break}return e.next=8,new Promise((function(e){return setTimeout(e,2e3)}));case 8:return e.abrupt("return",{data:{success:!0},status:200});case 9:return Date.now(),e.abrupt("return",window.fetch(n,s).then((function(e){return e.ok?e.text().then((function(t){try{var r=JSON.parse(t);return Date.now(),{data:r,status:e.status}}catch(r){var o=Ce()(Re()(Fe()(t))),i=new Error("Invalid server response. ".concat(o));throw i.detail={endpoint:n,data:o,status:e.status,error:r,text:t},i}})):Ee(e.headers.get("Content-Type"),"application/json")?e.text().then((function(t){try{return{data:JSON.parse(t),status:e.status}}catch(i){var r=Ce()(Re()(Fe()(t))),o=new Error("Invalid server response. ".concat(r));throw o.detail={endpoint:n,data:r,status:e.status,error:i,text:t},o}})):e.text().then((function(t){var r=Ce()(Re()(Fe()(t))),o=new Error("Unknown server response. ".concat(r));throw o.detail={endpoint:n,data:r,status:e.status},o}))})).catch((function(e){return{error:e}})));case 14:case"end":return e.stop()}}),e)})),qe=function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function c(e){z(i,n,o,c,u,"next",e)}function u(e){z(i,n,o,c,u,"throw",e)}c(void 0)}))},qe.apply(this,arguments)}function Ne(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ne(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ne(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _e(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return He(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?He(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,c=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return c=e.done,e},e:function(e){u=!0,i=e},f:function(){try{c||null==r.return||r.return()}finally{if(u)throw i}}}}function He(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var $e=function(e){if(null==e)return"null";if("string"===u(e))return JSON.stringify(e);if(!(0,l.isObject)(e)&&!Array.isArray(e))return String(e);if(Array.isArray(e))return"["+e.map((function(e){return $e(e)})).join(", ")+"]";var t=[];for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){var n=String(r),o=$e(e[r]);t.push("".concat(n,": ").concat(o))}return"{"+t.join(", ")+"}"},Ue=function(e,t){var r,n=[],o=_e(e);try{for(o.s();!(r=o.n()).done;){var i=r.value;if(Object.prototype.hasOwnProperty.call(i,"_alias")){var c=i._alias;if(n.push(c),c===t)return!1}}}catch(e){o.e(e)}finally{o.f()}return!(n.length<e.length-1)&&(!n.includes(t)&&new Set(n).size===n.length)},We=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=Object.entries(e).map((function(e){var t=o(e,2),r=t[0],n=t[1];return"".concat(r,": ").concat($e(n))})).join(", ");return r?"(".concat(r,")"):t?"()":""},Be=function(e,t){if(1===Object.keys(e).length&&e._alias)return"".concat(e._alias,": ").concat(t);if(1===Object.keys(e).length&&e._transform){var r=e._transform;if(!Object.prototype.hasOwnProperty.call(r,"_alias")||!Object.prototype.hasOwnProperty.call(r,"_args"))throw new Error("Transforms must have `_alias` and `_args` keys.");var n=We(r._args,!0),o="".concat(r._alias,": ").concat(t);return"".concat(o).concat(n)}var i=e._args||{},c=We(i),u=e._alias?"".concat(e._alias,": ").concat(t):t,a={};for(var s in e)s.startsWith("_")||(a[s]=e[s]);var f=Je(a);return"".concat(u).concat(c," ").concat(f)},Je=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=[];for(var r in e)if(!r.startsWith("_")){var n=e[r];if(Array.isArray(n)){if(!Ue(n,r))throw new Error("Aliases that do not match the key are required for an array of queries.");var o,i=_e(n);try{for(i.s();!(o=i.n()).done;){var c=o.value;t.push(Be(c,r))}}catch(e){i.e(e)}finally{i.f()}}else(0,l.isObject)(n)?t.push(Be(n,r)):!0===n&&t.push(r)}return"{".concat(t.join(", "),"}")},Ke=function(){return Je(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).trim()},ze=function(e){var t=e.from,r=e.to;if(!t||!r)throw new Error("Both from id and to id are required for connect mutations.");return{_args:{from:t,to:r}}},Qe="insert",Xe="update",Ye="delete",Ge="connect",Ve="disconnect",Ze=[Qe,Xe,Ye,Ge,Ve],et=s(s(s(s(s({},Qe,(function(e){var t=e.objects,r=void 0===t?[]:t,n=e.returning;return{_args:{objects:r},returning:void 0===n?{}:n}})),Xe,(function(e){var t=e.id,r=e._args,n=void 0===r?{}:r,o=e.returning,i=void 0===o?{}:o;if(!t)throw new Error("An id is required for update mutations.");return{_args:Le({id:t},n),returning:i}})),Ye,(function(e){var t=e.id;if(!t)throw new Error("An id is required for delete mutations.");return{_args:{id:t}}})),Ge,ze),Ve,ze),tt=f.React.useCallback,rt=f.React.useState,nt=function(e){var t=e.action,r=void 0===t?"":t,n=e.addCache,i=void 0===n?function(){}:n,c=e.buildCacheKey,u=void 0===c?function(){}:c,a=e.cache,f=void 0===a?{}:a,p=e.customMutationTypeMap,d=void 0===p?{}:p,v=e.endpoint,y=void 0===v?"":v,m=e.mutationAction,b=void 0===m?"":m,g=e.mutationKey,h=void 0===g?"mutation":g,O=e.mutationSecurity,w=void 0===O?"":O,j=e.queryAction,P=void 0===j?"":j,S=e.queryKey,E=void 0===S?"query":S,A=e.querySecurity,x=void 0===A?"":A,R=e.security,k=void 0===R?"":R,C=e.useCache,D=void 0!==C&&C,F=o(rt(0),2),I=F[0],T=F[1];if(!P&&!r)throw new Error("A `queryAction` or `action` is required.");if(!b&&!r)throw new Error("A `mutationAction` or `action` is required.");var M=P||r,q=b||r,N=x||k,L=w||k,_=tt((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"!=typeof e&&!(0,l.isObject)(e))throw new Error("Invalid query parameter.");var r="";"string"==typeof e?r=e:(0,l.isObject)(e)&&(r=Ke(e));var n=u(r);return D&&f[n]?Promise.resolve(f[n]):(T((function(e){return e+1})),Me({endpoint:y,body:s({action:M,security:N},E,r),options:t}).then((function(e){return D&&i(n,e),e})).finally((function(){T((function(e){return e-1}))})))}),[D,y,M,N,E,u,f,i]),H=tt((function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"!=typeof e&&!(0,l.isObject)(e))throw new Error("Invalid mutation parameter.");var n="";if("string"==typeof e)n=e;else if(Ze.includes(t))n=function(e,t){if(!Ze.includes(t))throw new Error('Invalid mutation type. Must be one of "insert", "update", "delete", "connect", or "disconnect".');var r=Object.keys(e);if(1!==r.length)throw new Error("Only a single key mutation object is allowed.");var n=r[0],o=et[t],i=s({},"".concat(t,"_").concat(n),o(e[n]));return Ke(i)}(e,t);else{if(!d[t])throw new Error("Invalid mutation type");n=d[t](e)}return T((function(e){return e+1})),Me({endpoint:y,body:s({action:q,security:L},h,n),options:r}).then((function(e){return e})).finally((function(){T((function(e){return e-1}))}))}),[d,y,q,L,h]);return{isLoading:I>0,query:_,mutation:H}};function ot(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function it(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ot(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var ct=f.React.createContext,ut=f.React.useContext,at=ct(null),st=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ut(at);if(!t)throw new Error("useHermesContext must be used within a HermesProvider.");return nt(it(it({},t),e))},ft=function(e){var t=e.defaultProps,r=void 0===t?{}:t,n=e.children;return f.React.createElement(at.Provider,{value:r},n)},lt=f.React.createContext,pt=f.React.useContext,dt=lt(null),vt=function(){return pt(dt)},yt=function(e){var t=e.children,r=e.id,n=d(void 0===r?"":r);return f.React.createElement(dt.Provider,{value:n},t)};function mt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}var bt=f.React.useEffect,gt=function(){bt((function(){var e;null===(e=window)||void 0===e||null===(e=e.wp)||void 0===e||e.media}),[]);return{openMediaManager:function(e){var t,r=e.contentMode,n=void 0===r?"browse":r,o=e.id,i=void 0===o?"":o,c=e.onMediaSelect,u=void 0===c?function(){}:c,a=e.options,f=void 0===a?{}:a;if(null!==(t=window)&&void 0!==t&&null!==(t=t.wp)&&void 0!==t&&t.media){var l=window.wp.media(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mt(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({library:{type:"image"},multiple:!1},f));l.on("open",(function(){l.content.mode(n)})),l.on("select",(function(){var e=l.state().get("selection"),t=null!=f&&f.multiple?e.map((function(e){return e.toJSON()})):e.first().toJSON();u&&u({attachments:t,id:i})})),l.open()}}}},ht=f.React.createContext,Ot=f.React.useContext,wt=f.React.useEffect,jt=f.React.useRef,Pt=ht(null),St=function(){var e=Ot(Pt);if(!e)throw new Error("useMediaManagerContext must be used within an MediaManagerProvider.");return e},Et=function(e){var t=e.children,r=jt(new Map),n=gt().openMediaManager,o=function(e){var t=e.detail.id,o=r.current.get(t);if(o){var i=o.onMediaSelect,c=o.options;n({id:t,onMediaSelect:i,options:c})}};wt((function(){return document.addEventListener("gform/file_upload/external_manager/open",o),function(){document.removeEventListener("gform/file_upload/external_manager/open",o)}}),[o]);var i={openMediaManager:n,registerMediaManager:function(e){var t=e.id,n=void 0===t?"":t,o=e.onMediaSelect,i=void 0===o?function(){}:o,c=e.options,u=void 0===c?{}:c;if(n)return r.current.set(n,{onMediaSelect:i,options:u}),function(){return r.current.delete(n)}}};return f.React.createElement(Pt.Provider,{value:i},t)};function At(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||n(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var kt=f.React.useEffect,Ct=f.React.useRef,Dt=f.React.useState,Ft=f.ReactRouter.useSearchParams,It=function(){var e=o(Dt([]),2),t=e[0],r=e[1],n=o(Dt({}),2),i=n[0],c=n[1],u=o(Ft(),2),a=u[0],f=u[1],l=Ct(a),p=Ct(!1);return kt((function(){p.current?p.current=!1:(t.forEach((function(e){var t=i[e];t&&t({searchParams:a,prevSearchParams:l.current})})),l.current=a)}),[a]),{addRouteHandler:function(e,n){e&&n&&(t.includes(e)||(c(Rt(Rt({},i),{},s({},e,n))),r([].concat(At(t),[e]))))},removeRouteHandler:function(e){e&&(c(Rt(Rt({},i),{},s({},e,void 0))),r(t.filter((function(t){return t!==e}))))},setRoute:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};p.current=!0,f(e,t)},searchParams:a,setSearchParams:f}},Tt=f.React.createContext,Mt=f.React.useContext,qt=Tt(null),Nt=function(){var e=Mt(qt);if(!e)throw new Error("useRouteHandlerContext must be used within an RouteHandlerProvider.");return e},Lt=function(e){var t=e.children,r=It();return f.React.createElement(qt.Provider,{value:r},t)},_t=f.React.createContext,Ht=f.React.useContext,$t=f.React.useState,Ut=_t(null),Wt=function(e){var t=Ht(Ut);if(!t)throw new Error("Missing StoreProvider.");return t(e)},Bt=function(e){var t=e.children,r=e.initialState,n=e.createStore,i=o($t((function(){return n(r)})),1)[0];return f.React.createElement(Ut.Provider,{value:i},t)},Jt=/input|select|textarea|button|object/,Kt="a, input, select, textarea, button, object, [tabindex]";function zt(e){var t=e.getAttribute("tabindex");return null===t&&(t=void 0),parseInt(t,10)}function Qt(e){var t=e.nodeName.toLowerCase(),r=!isNaN(zt(e));return(Jt.test(t)&&!e.disabled||e instanceof HTMLAnchorElement&&e.href||r)&&function(e){for(var t,r=e;r&&r!==document.body;){if(t=r,!(0,l.isJestTest)()&&(t.offsetWidth<=0&&t.offsetHeight<=0||"none"===t.style.display))return!1;r=r.parentNode}return!0}(e)}function Xt(e){var t=zt(e);return(isNaN(t)||t>=0)&&Qt(e)}function Yt(e){return Array.from(e.querySelectorAll(Kt)).filter(Xt)}var Gt=f.React.useCallback,Vt=f.React.useEffect,Zt=f.React.useRef;var er=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Zt(),n=Zt(null),o=function(){var e=[],t=null,r=!1;function n(){r=!0}function o(){r&&(r=!1,t&&(t.contains(document.activeElement)||(Yt(t)[0]||t).focus()))}return{markForFocusLater:function(){e.push(document.activeElement)},returnFocus:function(){var t=null;try{(t=e.pop())&&setTimeout((function(){return t.focus()}))}catch(e){}},setupScopedFocus:function(e){(t=e).addEventListener("focusout",n,!1),t.addEventListener("focusin",o,!0)},teardownScopedFocus:function(){t.removeEventListener("focusout",n),t.removeEventListener("focusin",o),t=null}}}(),i=o.markForFocusLater,c=o.returnFocus,u=o.setupScopedFocus,a=o.teardownScopedFocus,s=Gt((function(o){if(n.current&&n.current(),r.current&&(c(),a()),e&&o){u(o),i();var s=function(e){n.current=t.disableAriaHider?null:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"body > :not(script)",r=Array.from(document.querySelectorAll(t)).map((function(t){if(!t.contains(e)){var r=t.getAttribute("aria-hidden");return null!==r&&"false"!==r||t.setAttribute("aria-hidden","true"),{node:t,ariaHidden:r}}}));return function(){r.forEach((function(e){e&&(null===e.ariaHidden?e.node.removeAttribute("aria-hidden"):e.node.setAttribute("aria-hidden",e.ariaHidden))}))}}(e);var r=null;if(t.focusSelector&&(r="string"==typeof t.focusSelector?e.querySelector(t.focusSelector):t.focusSelector),!r){var o=Array.from(e.querySelectorAll(Kt));!(r=o.find(Xt)||o.find(Qt)||null)&&Qt(e)&&(r=e)}r&&r.focus()};setTimeout((function(){o.ownerDocument&&s(o)})),r.current=o}else r.current=null}),[e,t.focusSelector,t.disableAriaHider]);return Vt((function(){if(e){var t=function(e){"Tab"===e.key&&r.current&&function(e,t){var r=Yt(e);if(r.length){if(r[t.shiftKey?0:r.length-1]===document.activeElement||e===document.activeElement){t.preventDefault();var n=r[t.shiftKey?r.length-1:0];n&&n.focus()}}else t.preventDefault()}(r.current,e)};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}}),[e]),s},tr=f.React.useEffect,rr=function(e){e.classList.add("current");var t=e.querySelector("a");t.classList.add("current"),t.setAttribute("aria-current","page")},nr=function(e){e.classList.remove("current");var t=e.querySelector("a");t.classList.remove("current"),t.removeAttribute("aria-current")},or=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{updateCurrent:!0},n=document.getElementById(e),o=function(e){var t=n.querySelectorAll("li:not(.wp-submenu-head)"),r=e.getAttribute("href");t.forEach(nr),e.classList.contains("menu-top")?t.forEach((function(e){var t;(null===(t=e.querySelector("a"))||void 0===t?void 0:t.getAttribute("href"))===r&&rr(e)})):rr(e.parentElement)};return tr((function(){if(n){var e=function(e){e.preventDefault();var n=e.currentTarget,i=n.getAttribute("href");r.updateCurrent&&o(n);var c=new URLSearchParams(i.split("?")[1]);t(c)},i=n.querySelectorAll("a");return i.forEach((function(t){t.addEventListener("click",e)})),function(){i.forEach((function(t){t.removeEventListener("click",e)}))}}}),[e,t]),{updateCurrentMenuItem:o}},ir=f.React.useEffect,cr=f.React.useState,ur=function(e){var t=e.closeOnClickOutside,r=void 0===t||t,n=e.customClickOutsideLogic,i=void 0===n?function(){}:n,c=e.duration,u=void 0===c?150:c,a=e.initialOpen,s=void 0!==a&&a,f=e.onAfterClose,l=void 0===f?function(){}:f,p=e.onAfterOpen,d=void 0===p?function(){}:p,v=e.onClose,y=void 0===v?function(){}:v,m=e.onOpen,b=void 0===m?function(){}:m,g=e.popupRef,h=void 0===g?null:g,O=e.triggerRef,w=void 0===O?null:O,j=o(cr(!1),2),P=j[0],S=j[1],E=o(cr(!1),2),A=E[0],x=E[1],R=o(cr(s),2),k=R[0],C=R[1];ir((function(){if(r){var e=function(e){k&&null!=w&&w.current&&null!=h&&h.current&&(w.current.contains(e.target)||h.current.contains(e.target)||i(e)||D())};return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}}}),[k,h,w]);var D=function(){y(),C(!1),x(!0),setTimeout((function(){x(!1),l()}),u)};return{closePopup:D,openPopup:function(){b(),S(!0),requestAnimationFrame((function(){C(!0),setTimeout((function(){S(!1),d()}),u)}))},handleEscKeyDown:function(e){var t;"Escape"===e.key&&(D(),null==w||null===(t=w.current)||void 0===t||t.focus())},popupHide:A,popupOpen:k,popupReveal:P,setPopupHide:x,setPopupOpen:C,setPopupReveal:S}},ar=f.React.useEffect,sr=function(e){var t=e.id,r=e.onMediaSelect,n=e.options,o=St().registerMediaManager;ar((function(){return o({id:t,onMediaSelect:r,options:n})}),[t,r,n,o])};var fr=["src","checkForExisting"],lr=f.React.useState,pr=f.React.useEffect,dr="undefined"!=typeof window&&void 0!==window.document,vr={},yr=function(e){var t=document.querySelector('script[src="'.concat(e,'"]'));if(t)return vr[e]={loading:!1,error:null,scriptEl:t}};function mr(e){var t=e.src,r=e.checkForExisting,n=void 0!==r&&r,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fr),c=t?vr[t]:void 0;!c&&n&&t&&dr&&(c=yr(t));var u=o(lr(c?c.loading:Boolean(t)),2),a=u[0],s=u[1],f=o(lr(c?c.error:null),2),l=f[0],p=f[1];return pr((function(){if(dr&&t&&a&&!l){var e;!(c=vr[t])&&n&&(c=yr(t)),c?e=c.scriptEl:((e=document.createElement("script")).src=t,Object.keys(i).forEach((function(t){void 0===e[t]?e.setAttribute(t,i[t]):e[t]=i[t]})),c=vr[t]={loading:!0,error:null,scriptEl:e});var r=function(){c&&(c.loading=!1),s(!1)},o=function(e){c&&(c.error=e),p(e)};return e.addEventListener("load",r),e.addEventListener("error",o),document.body.appendChild(e),function(){e.removeEventListener("load",r),e.removeEventListener("error",o)}}}),[t]),[a,l]}function br(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function gr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?br(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):br(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var hr=function(e,t){var r=(0,f.zustand)((function(r){return gr(gr({},e),t(r))}));return r.getData=function(){var t=r.getState();return(0,l.filterObject)(t,(function(t){var r=o(t,1)[0];return void 0!==e[r]}))},r.getActions=function(){var e=r.getState(),n=t();return(0,l.filterObject)(e,(function(e){var t=o(e,1)[0];return void 0!==n[t]}))},r},Or=f.React.useState,wr=f.React.useEffect;function jr(e){var t=o(Or(e),2),r=t[0],n=t[1];return wr((function(){n(e)}),[e]),[r,n]}!function(){var t=window.gformComponentNamespace||"gform";window[t]=window[t]||{},window[t].utils=window[t].utils||{},window[t].utils.react=window[t].utils.react||{};var r=window[t].utils.react;Object.entries(e).forEach((function(e){var t=o(e,2),n=t[0],i=t[1];r[n]=i}))}()}()}();