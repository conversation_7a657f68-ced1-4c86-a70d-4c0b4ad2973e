"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[940],{6940:function(e,t,i){i.d(t,{A:function(){return ee}});var r,n,a,o,l,s,d,c,u,p,g,f,m,v=i(6020),b=i(9662),h=i.n(b),y=i(1533),_=i.n(y),w=i(428),O=i(7195),j=i.n(O),A=i(5798),L=i(8134),T=i(2001),x=i.n(T),C=(null===(r=window)||void 0===r?void 0:r.form)||{},P=(null===(n=window)||void 0===n?void 0:n.GetInputType)||null,E=(null===(a=window)||void 0===a?void 0:a.<PERSON>abel)||null,I=(null===(o=window)||void 0===o?void 0:o.GetInput)||null,F=(null===(l=window)||void 0===l?void 0:l.Copy)||null,q=(null===(s=window)||void 0===s?void 0:s.IsPricingField)||null,k=(null===(d=window)||void 0===d?void 0:d.HasPostField)||null,S=function(e,t){var i=e.classList.value;if(!i)return"";var r=i.split(" ");for(var n in r)if(Object.prototype.hasOwnProperty.call(r,n)){var a=r[n].split("-");if("mt"==a[0]&&a[1]==t)return a.length>3?(delete a[0],delete a[1],a):2===a.length||a[2]}return""},D=function(e){for(var t in x().mergeTags)if(Object.prototype.hasOwnProperty.call(x().mergeTags,t)){var i=x().mergeTags[t].tags;for(var r in i)if(Object.prototype.hasOwnProperty.call(i,r)&&i[r].tag==e)return i[r].label}return""},z=function(e){return x().mergeTags[e].label},B=function(e,t){void 0===t&&(t="");var i=[],r=P(e),n="list"===r?":"+t:"",a="radio"===r&&w.inArray(e.type,["multi_choice","image_choice"])>-1,o="",l="",s="";if(w.inArray(r,["date","email","time","password"])>-1&&(s=e.inputs,e.inputs=null),void 0!==e.inputs&&w.isArray(e.inputs)&&!a){for(var d in"checkbox"===r&&(o="{"+(l=E(e,e.id).replace("'","\\'"))+":"+e.id+n+"}",i.push({tag:o,label:l})),e.inputs)if(Object.prototype.hasOwnProperty.call(e.inputs,d)){var c=e.inputs[d];"creditcard"===r&&w.inArray(parseFloat(c.id),[parseFloat(e.id+".2"),parseFloat(e.id+".3"),parseFloat(e.id+".5")])>-1||(o="{"+(l=E(e,c.id).replace("'","\\'"))+":"+c.id+n+"}",i.push({tag:o,label:l}))}}else o="{"+(l=E(e).replace("'","\\'"))+":"+e.id+n+"}",i.push({tag:o,label:l});return w.inArray(r,["date","email","time","password"])>-1&&(e.inputs=s),i},H=function(e,t,i,r,n,a){void 0===e&&(e=[]),void 0===r&&(r=[]);var o=[],l=[],s=[],d=[],c=[],u=[],p=[],g=[],f=[];if(i||s.push({tag:"{all_fields}",label:D("{all_fields}")}),!n){for(var m in e)if(Object.prototype.hasOwnProperty.call(e,m)){var v=e[m];if(!v.displayOnly){var b=P(v);if(-1===w.inArray(b,r)){if(v.isRequired)if("name"===b){var y=F(v),_=void 0,O=void 0,j=void 0,A=void 0;"extended"===v.nameFormat?(_=I(v,v.id+".2"),j=I(v,v.id+".8"),(A=F(v)).inputs=[_,j],l.push(A),delete y.inputs[0],delete y.inputs[3]):"advanced"===v.nameFormat&&(_=I(v,v.id+".2"),O=I(v,v.id+".4"),j=I(v,v.id+".8"),(A=F(v)).inputs=[_,O,j],l.push(A),delete y.inputs[0],delete y.inputs[2],delete y.inputs[4]),o.push(y)}else o.push(v);else l.push(v);q(v.type)&&p.push(v)}}}if(o.length>0)for(var L in o)Object.prototype.hasOwnProperty.call(o,L)&&(g=g.concat(B(o[L],a)));if(l.length>0)for(var T in l)Object.prototype.hasOwnProperty.call(l,T)&&"submit"!==l[T].type&&(f=f.concat(B(l[T],a)));if(p.length>0)for(var C in i||d.push({tag:"{pricing_fields}",label:D("{pricing_fields}")}),p)Object.prototype.hasOwnProperty.call(p,C)&&d.concat(B(p[C],a))}var E=["ip","date_mdy","date_dmy","embed_post:ID","embed_post:post_title","embed_url","entry_id","entry_url","form_id","form_title","user_agent","referer","post_id","post_edit_url","user:display_name","user:user_email","user:user_login"];for(var S in n&&(E.splice(E.indexOf("entry_id"),1),E.splice(E.indexOf("entry_url"),1),E.splice(E.indexOf("form_id"),1),E.splice(E.indexOf("form_title"),1)),k()&&!n||(E.splice(E.indexOf("post_id"),1),E.splice(E.indexOf("post_edit_url"),1)),E)-1===w.inArray(E[S],r)&&c.push({tag:"{"+E[S]+"}",label:D("{"+E[S]+"}")});var H=function(){for(var e in x().mergeTags)if(Object.prototype.hasOwnProperty.call(x().mergeTags,e)&&"custom"===e)return x().mergeTags[e];return[]}();if(H.tags.length>0)for(var G in H.tags)if(Object.prototype.hasOwnProperty.call(H.tags,G)){var V=H.tags[G];u.push({tag:V.tag,label:V.label})}var N={ungrouped:{label:z("ungrouped"),tags:s},required:{label:z("required"),tags:g},optional:{label:z("optional"),tags:f},pricing:{label:z("pricing"),tags:d},other:{label:z("other"),tags:c},custom:{label:z("custom"),tags:u}};return N=h().applyFilters("gform_merge_tags",N,t,i,r,n,a,undefined)},G=function(e){var t="number"===e.type&&e.enableCalculation,i="product"===e.type&&"calculation"===e.inputType&&e.enableCalculation,r="post_custom_field"===e.type&&"number"===e.inputType;return t||i||r},V=function(e,t,i){var r=[],n=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return h().applyFilters("gform_merge_tags_supported_input_types",["text","select","number","checkbox","radio","hidden","singleproduct","price","hiddenproduct","calculation","singleshipping"],e,t,i)}(e,t,i);return t.forEach(function(e){if(e.inputType||(e.inputType=e.type),n.includes(e.inputType))if("checkbox"===e.inputType&&e.choices){var t=1;e.choices.forEach(function(i){r.push({value:"{".concat(i.text,":").concat(e.id,".").concat(t,"}"),label:i.text});do{t++}while(t%10==0)})}else"product"===e.type&&e.inputs?e.inputs.forEach(function(t){r.push({value:"{".concat(e.label," (").concat(t.label,"):").concat(t.id,"}"),label:"".concat(e.label," (").concat(t.label,")")})}):r.push({value:"{".concat(e.label,":").concat(e.id,"}"),label:e.label})}),h().applyFilters("gform_custom_merge_tags",r,e,t,i)},N=function(e){var t=C.fields,i=e.getAttribute("id"),r=e.classList.contains("merge-tag-calculation"),n=t.some(function(e){return G(e)}),a=[];if(r&&n)return V(C.id,t,i);r&&(c=new Event("gform/layout_editor/merge_tags_first_load"),document.dispatchEvent(c));var o=!0===S(e,"hide_all_fields"),l=S(e,"exclude"),s=S(e,"prepopulate"),d=S(e,"option");s&&(o=!0);var u=H(t,i,o,l,s,d),p=function(e){var t=0;for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&e[i].tags.length>0&&t++;return t>1}(u);for(var g in u)if(Object.prototype.hasOwnProperty.call(u,g)){var f=u[g].label,m=u[g].tags,v=f&&p;if(!(m.length<=0)){var b=m.map(function(e){return{value:e.tag.replace(/"/g,"&quot;"),label:h().tools.stripSlashes(e.label)}});v?a.push({label:f,listData:b}):a.push.apply(a,(0,L.A)(b))}}return a},M=(null===_()||void 0===_()||null===(u=_().components)||void 0===u?void 0:u.merge_tags)||{},R=(null===(p=window)||void 0===p?void 0:p.InsertVariable)||null,J=(null===(g=window)||void 0===g?void 0:g.InsertEditorVariable)||null,K=(null===(f=window)||void 0===f?void 0:f.form)||{},Q=function(e){this.isEditor?J(this.elem.getAttribute("id"),e):R(this.elem.getAttribute("id"),null,e),w(this.elem).trigger("input").trigger("propertychange")},U=function(){if(h().simplebar.initializeInstances(),function(e){var t=document.querySelector('[data-js="'.concat(e,'"]')),i=(0,A.getClosest)(t,".panel-block-tabs__body");if(i){var r=250-i.offsetHeight,n=window.getComputedStyle(i).getPropertyValue("padding-bottom");r<10||(i.setAttribute("data-js-initial-padding",n),i.style.paddingBottom="".concat(r,"px"))}}(this.selector),(0,A.browsers)().firefox){var e=document.querySelector('[data-js="'.concat(this.selector,'"]'));e.querySelector(".gform-dropdown__container").removeAttribute("style"),A.simpleBar.reInitChildren(e)}},W=function(){var e,t,i;e=this.selector,t=document.querySelector('[data-js="'.concat(e,'"]')),(i=(0,A.getClosest)(t,".panel-block-tabs__body"))&&i.hasAttribute("data-js-initial-padding")&&(i.style.paddingBottom=i.getAttribute("data-js-initial-padding"),i.removeAttribute("data-js-initial-padding"))},X=function(e,t){var i=N(e),r=S(e,"manual_position"),n=r?function(e){var t=(0,A.getClosest)(e,".wp-editor-wrap").querySelector(".wp-media-buttons");return(0,A.getChildren)(t).slice(-1).pop()}(e):e,a=function(e,t){var i=S(e,"manual_position"),r=document.createElement("span");return r.classList.add("all-merge-tags"),r.classList.add("gform-merge-tags-dropdown-wrapper"),r.classList.add(e.tagName.toLowerCase()),i?r.classList.add("left"):r.classList.add("right"),r.setAttribute("mt-dropdown-".concat(t),!0),r.innerHTML='<span data-js="gform-dropdown-mt-wrapper-'.concat(t,'"></span>'),r}(e,t);(0,A.insertAfter)(a,n),h().instances.mergeTags.push(new(j())({attributes:'data-js-input-id="'.concat(e.getAttribute("id"),'"'),container:"mt-dropdown-".concat(t),selector:"gform-dropdown-mt-".concat(t),renderTarget:'[data-js="gform-dropdown-mt-wrapper-'.concat(t,'"]'),swapLabel:!1,listData:i,render:!0,triggerPlaceholder:(0,A.saferHtml)(m||(m=(0,v.A)(['<i class="gform-icon gform-icon--merge-tag gform-button__icon"></i>']))),triggerTitle:M.i18n.insert_merge_tags,wrapperClasses:"gform-dropdown gform-dropdown--merge-tags",triggerId:"mt-dropdown--trigger-".concat(t),triggerAriaId:"mt-dropdown--trigger-label-".concat(t),triggerClasses:"ui-state-disabled",onItemSelect:Q.bind({isEditor:r,idx:t,elem:e}),searchPlaceholder:M.i18n.search_merge_tags,onOpen:U.bind({selector:"gform-dropdown-mt-".concat(t)}),onClose:W.bind({selector:"gform-dropdown-mt-".concat(t)}),dropdownListAttributes:'data-js="gform-simplebar"',hasSearch:!n.classList.contains("merge-tag-calculation")}))},Y=function(){(0,A.getNodes)(".merge-tag-support:not(.mt-initialized)",!0,document,!0).forEach(function(e){var t=(0,A.uniqueId)();X(e,t),function(e){var t=(0,A.getClosest)(e,".field_setting"),i=(0,A.getClosest)(e,".gform-settings-field");t?t.classList.add("field_setting--with-merge-tag"):i&&i.classList.add("gform-settings-field--with-merge-tag")}(e),e.classList.add("mt-initialized")})},Z=function(){var e,t=K.fields,i=$(),r=(null==i||null===(e=i.elements)||void 0===e?void 0:e.container)||null,n=r?r.getAttribute("data-js-input-id"):null,a=t.some(function(e){return G(e)}),o=V(K.id,t,n);a&&(i.options.listData=o,i.renderListData())},$=function(){var e=document.getElementById("calculation_options");if(!e)return null;for(var t=0;t<h().instances.mergeTags.length;t++){var i=h().instances.mergeTags[t],r=i.elements.container;if(e.contains(r))return i}return null},ee=function(){h().instances=(null===h()||void 0===h()?void 0:h().instances)||{},h().instances.mergeTags=h().instances.mergeTags||[],h().components=(null===h()||void 0===h()?void 0:h().components)||{},document.addEventListener("gform/merge_tag/initialize",Y),document.addEventListener("gform/form_editor/toggle_calculation_options",Z),document.addEventListener("gform/layout_editor/field_modified",Z),document.addEventListener("gform/form_editor/set_field_label",Z),document.addEventListener("gform/layout_editor/gform_field_deleted",Z),Y()}}}]);