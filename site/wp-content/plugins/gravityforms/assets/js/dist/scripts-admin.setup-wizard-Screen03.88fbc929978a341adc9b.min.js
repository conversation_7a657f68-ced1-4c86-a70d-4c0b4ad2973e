"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[100],{7526:function(e,t,a){a.r(t);var r=a(8140),n=a(7616),i=a(9877),s=a.n(i),o=a(484),c=a.n(o),l=a(6648),u=a.n(l),m=a(7510),f=a.n(m),p=a(3841),d=a.n(p),g=a(2368),z=a.n(g),h=a(2225),_=a.n(h),w=a(7290),C=a(8335),v=n.React.useState;t.default=function(e){var t=e.data,a=e.i18n,i=v(!1),o=(0,r.A)(i,1)[0],l=(0,w.default)(function(e){return e.activeStep}),m=(0,w.default)(function(e){return e.setFormTypesOther}),p=(0,w.default)(function(e){return e.setOrganizationOther}),g=(0,w.default)(function(e){return e.setServicesOther}),h=a.describe_organization,x=a.form_type,y=a.next,R=a.personalize_copy,E=a.personalize_title,b=a.services_connect,k=a.other_label,A=a.other_placeholder,N=a.previous,T={ref:(0,C.useFocusTrap)(3===l),className:(0,n.classnames)({"gform-setup-wizard__screen":!0,"gform-setup-wizard__screen--step-3":!0}),"aria-hidden":3!==l};return n.React.createElement("div",T,n.React.createElement("div",{className:"gform-setup-wizard__content"},n.React.createElement(u(),{content:E,customClasses:["gform-typography--md-size-display-sm"],spacing:{"":3,md:5},size:"display-xs",weight:"medium",tagName:"h2"}),n.React.createElement(_(),{content:R,spacing:{"":5,md:8},size:"text-md",weight:"regular"}),n.React.createElement(u(),{content:h,customClasses:["gform-typography--md-text-size-xl"],size:"text-lg",spacing:{"":3,md:5},tagName:"h3",weight:"medium"}),n.React.createElement(s(),{customClasses:["gform-setup-wizard__organization-container"],setDisplay:!1,spacing:{"":6,md:8}},n.React.createElement(z(),{customClasses:(0,n.classnames)("gform-setup-wizard__organization",{"gform-setup-wizard__organization--is-placeholder":!(0,w.default)(function(e){return e.organization})}),initialValue:(0,w.default)(function(e){return e.organization}),onChange:(0,w.default)(function(e){return e.setOrganization}),options:t.options.organization,ariaLabel:h,size:"size-xl"}),"other"===(0,w.default)(function(e){return e.organization})&&n.React.createElement(s(),{customClasses:["gform-setup-wizard__other-container"]},n.React.createElement(f(),{labelAttributes:{label:k},onChange:p,placeholder:A,size:"size-xl"}))),n.React.createElement(u(),{content:x,customClasses:["gform-typography--md-text-size-xl"],size:"text-lg",weight:"medium",tagName:"h3"}),n.React.createElement(s(),{customClasses:["gform-setup-wizard__form-types","gform-setup-wizard__input-group"],spacing:{"":6,md:8}},n.React.createElement(d(),{id:"setup-wizard-form-types",data:(0,w.default)(function(e){return e.formTypes}),onChange:(0,w.default)(function(e){return e.patchFormTypes}),useWrapper:!0}),(0,w.default)(function(e){return e.formTypes}).filter(function(e){return e.initialChecked}).map(function(e){return e.value}).includes("other")&&n.React.createElement(s(),{customClasses:["gform-setup-wizard__other-container"]},n.React.createElement(f(),{labelAttributes:{label:k},onChange:m,placeholder:A,size:"size-xl"}))),n.React.createElement(u(),{content:b,customClasses:["gform-typography--md-text-size-xl"],size:"text-lg",weight:"medium",tagName:"h3"}),n.React.createElement(s(),{customClasses:["gform-setup-wizard__services-container","gform-setup-wizard__input-group"],spacing:{"":6,md:8}},n.React.createElement(d(),{id:"setup-wizard-services",data:(0,w.default)(function(e){return e.services}),onChange:(0,w.default)(function(e){return e.patchServices}),useWrapper:!0}),(0,w.default)(function(e){return e.services}).filter(function(e){return e.initialChecked}).map(function(e){return e.value}).includes("other")&&n.React.createElement(s(),{customClasses:["gform-setup-wizard__other-container"]},n.React.createElement(f(),{labelAttributes:{label:k},onChange:g,placeholder:A,size:"size-xl"}))),n.React.createElement(s(),{x:850,customClasses:["gform-setup-wizard__footer"],display:"flex"},n.React.createElement(c(),{size:"size-height-xl",type:"white",icon:"arrow-narrow-left",iconPrefix:"gform-common-icon",onClick:(0,w.default)(function(e){return e.setActiveStepPrevious}),ariaLabel:N}),n.React.createElement(c(),{size:"size-height-xl",customClasses:["gform-setup-wizard__nav-next-alt"],label:y,activeText:y,icon:"arrow-narrow-right",iconAttributes:{customClasses:["gform-button__icon--inactive"]},iconPrefix:"gform-common-icon",active:o,activeType:"loader",disabled:o,iconPosition:"trailing",onClick:(0,w.default)(function(e){return e.setActiveStepNext})}))))}}}]);