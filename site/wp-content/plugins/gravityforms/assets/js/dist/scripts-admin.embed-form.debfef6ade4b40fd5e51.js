"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[848],{2671:function(e,n,t){t.r(n),t.d(n,{default:function(){return m}});var r,o=t(5518),c=t(7063),i=t(2340),s=t.n(i),u=t(7329),f=t.n(u),p=t(1783);function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}var l=(null===f()||void 0===f()||null===(r=f().components)||void 0===r?void 0:r.embed_form)||{};s().instances=(null===s()||void 0===s()?void 0:s().instances)||{},s().components=(null===s()||void 0===s()?void 0:s().components)||{};var b=function(){s().instances.embedForm=new p.Z(function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){(0,c.Z)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}({},l))},m=function(){b(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized embed form component.")}}}]);
//# sourceMappingURL=scripts-admin.embed-form.debfef6ade4b40fd5e51.js.map