"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[376],{1154:function(n,e,t){t.r(e),t.d(e,{default:function(){return m}});var o,s=t(5518),i=t(191),c=t.n(i),r=t(2340),p=t.n(r),a=t(7329),d=t.n(a),l=(null===d()||void 0===d()||null===(o=d().components)||void 0===o?void 0:o.post_select)||{},u=function(n){var e=(0,s.getClosest)(n,".gform-settings-field"),t=n.dataset.postType,o=l[t],i=o.endpoints,r=o.data;p().instances.postSelects.push(new(c())({container:e.id,selector:"gform-settings-field-select",render:!1,renderListData:!0,searchType:"async",onItemSelect:function(n){return function(n,e){(0,s.getNodes)("gf-post-select-input",!0,e,!1)[0].value=n}(n,e)},baseUrl:i.get,endpoints:i,endpointKey:"get",endpointRequestOptions:{method:"GET"},endpointUseRest:!0,listData:r}))},f=function(n){!function(n){p().components.Dropdown||(p().components.Dropdown=c()),p().instances.postSelects=[],n.forEach((function(n){u(n)}))}(n)},m=function(n){f(n),(0,s.consoleInfo)("Gravity Forms Admin: Initialized post select dropdown component.")}}}]);