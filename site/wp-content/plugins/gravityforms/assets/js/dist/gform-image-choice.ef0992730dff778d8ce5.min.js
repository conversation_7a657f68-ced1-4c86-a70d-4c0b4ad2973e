"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[952],{8398:function(e,t,c){c.r(t);var i=c(5798),a=function(e){var t=e.delegateTarget,c=(0,i.getNode)(".gfield-choice-input",t,!0);if(!c.disabled&&("INPUT"!==e.target.tagName||"checkbox"!==e.target.type&&"radio"!==e.target.type)&&"LABEL"!==e.target.tagName){"checkbox"===c.type&&(c.checked=!c.checked),"radio"===c.type&&(c.checked=!0);var a=new Event("change",{bubbles:!0});c.dispatchEvent(a);var o=c.id.split("_"),d=o[1],g=o[2];window.gform.doAction("gform_input_change",c,d,g),c.focus()}};t.default=function(){(0,i.delegate)(document.body,".gfield--type-image_choice .gchoice","click",a),(0,i.consoleInfo)("Gravity Forms Theme: Initialized image choice field.")}}}]);