"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[236],{1786:function(e,t,r){r.r(t);var o,a,n=r(8349),i=r(6285),l=r(7329),d=r.n(l),c=r(5518),m=n.ReactDOM.createRoot,s=(null===d()||void 0===d()||null===(o=d().components)||void 0===o?void 0:o.template_library)||{},u=(null===d()||void 0===d()||null===(a=d().apps)||void 0===a?void 0:a.template_library)||{},p={templateLibraryTrigger:(0,c.getNode)("gform-add-new-form"),root:(0,c.getNode)(u.root_element)},g=function(){(0,c.trigger)({event:"gform/template_library/set_open_status",el:document,data:{isOpen:!0},native:!1})};t.default=function(){m(p.root).render(n.React.createElement(i.Z,s)),p.templateLibraryTrigger.addEventListener("click",g)}}}]);
//# sourceMappingURL=scripts-admin.template-library.7e8ecd7dce8d6eae810a.js.map