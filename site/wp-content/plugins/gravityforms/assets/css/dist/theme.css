/*
----------------------------------------------------------------

theme.css
Gravity Theme Styles
A light theme for the frontend engineered to get reasonably
nice look and feel in all our standard theme targets.
https://www.gravityforms.com

Theme dependencies:
- Gravity Forms Basic Theme: basic.css

Gravity Forms is a Rocketgenius project
copyright 2008-2024 Rocketgenius Inc.
https://www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

/* Gravity Theme */

/*
* Styles for labels and legends, including making legends look like labels.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_label {
		display: inline-block;
		font-size: 16px;
		font-weight: 700;
		margin-bottom: 8px;
		padding: 0;
	}

.gform_wrapper.gravity-theme .ginput_complex label, .gform_wrapper.gravity-theme .gform_fileupload_rules, .gform_wrapper.gravity-theme .gfield_header_item {
		font-size: 15px;
		padding-top: 5px;
	}

.gform_wrapper.gravity-theme.left_label fieldset.gfield, .gform_wrapper.gravity-theme.right_label fieldset.gfield {
			padding: 16px 16px 16px 30%;
		}

.gform_wrapper.gravity-theme.left_label fieldset.gfield .ginput_container:not(.ginput_container_time), .gform_wrapper.gravity-theme.left_label fieldset.gfield .gfield_description, .gform_wrapper.gravity-theme.right_label fieldset.gfield .ginput_container:not(.ginput_container_time), .gform_wrapper.gravity-theme.right_label fieldset.gfield .gfield_description {
				width: 100%;
			}

.gform_wrapper.gravity-theme.left_label legend.gfield_label, .gform_wrapper.gravity-theme.right_label legend.gfield_label {
			left: 0;
		}

.gform_wrapper.gravity-theme.left_label .gfield:not(.gsection):not(.gfield_html):not(fieldset), .gform_wrapper.gravity-theme.right_label .gfield:not(.gsection):not(.gfield_html):not(fieldset) {
			justify-content: flex-start;
		}

.gform_wrapper.gravity-theme.left_label .gfield_html.gfield_html_formatted, .gform_wrapper.gravity-theme.right_label .gfield_html.gfield_html_formatted {
			display: flex;
		}

.gform_wrapper.gravity-theme.left_label .gfield.gfield_html:not(.gfield_html_formatted) .gf-html-container, .gform_wrapper.gravity-theme.right_label .gfield.gfield_html:not(.gfield_html_formatted) .gf-html-container {
			width: 100%;
		}

.gform_wrapper.gravity-theme.right_label legend.gfield_label {
			text-align: right;
		}

.gform_wrapper.gravity-theme:not(.top_label) .ginput_container.ginput_single_email {
		margin-left: 0;
	}

/*
* Theme css for the validation messages.
*
* Used in the theme.css front-end stylesheet.
*/

.gform_wrapper.gravity-theme .gform_validation_errors {
	background: #fff9f9;
	border: 1.5px solid #c02b0a;
	border-radius: 5px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.11), 0 0 4px rgba(18, 25, 97, 0.0405344);
	margin-bottom: 8px;
	margin-top: 8px;
	padding: 16px 16px 16px 48px;
	position: relative;
	width: 100%;
}

.gform_wrapper.gravity-theme .gform_validation_errors > h2 {
		color: #c02b0a;
		font-size: 13.2px;
		font-weight: 500;
		line-height: 17.6px;
		margin: 0 0 12px 0;
	}

.gform_wrapper.gravity-theme .gform_validation_errors > h2::before {
			display: none;
		}

.gform_wrapper.gravity-theme .gform_validation_errors > h2 .gform-icon {
			align-items: center;
			display: flex;
			font-size: 28px;
			height: 100%;
			left: 12px;
			position: absolute;
			top: 0;
		}

.gform_wrapper.gravity-theme .gform_validation_errors > h2.hide_summary {
		margin: 0;
	}

.gform_wrapper.gravity-theme .gform_validation_errors > ol {
		padding-left: 20px;
	}

.gform_wrapper.gravity-theme .gform_validation_errors > ol a {
			color: #c02b0a;
			font-size: 13.2px;
		}

.gform_wrapper.gravity-theme .gform_validation_errors > ol li {
			color: #c02b0a;
			font-size: 13.2px;
			list-style-type: disc !important;
			margin: 0 0 8px 0;
		}

.gform_wrapper.gravity-theme .gform_validation_errors > ol li:last-of-type {
				margin-bottom: 0;
			}

.gform_wrapper.gravity-theme .gfield_required {
	color: #c02b0a;
	display: inline-block;
	font-size: 13.008px;
	padding-left: 0.125em;
}

.gform_wrapper.gravity-theme .gfield_required .gfield_required_text, .gform_wrapper.gravity-theme .gfield_required .gfield_required_custom {
		font-style: italic;
		font-weight: 400;
	}

.gform_wrapper.gravity-theme .gform_required_legend .gfield_required {
	padding-left: 0;
}

.gform_wrapper.gravity-theme .validation_error {
	border-bottom: 2px solid #c02b0a;
	border-top: 2px solid #c02b0a;
	color: #c02b0a;
	font-size: 1em;
	font-weight: 700;
	margin-bottom: 32px;
	padding: 16px;
	text-align: center;
}

.gform_wrapper.gravity-theme [aria-invalid="true"] + label, .gform_wrapper.gravity-theme label + [aria-invalid="true"], .gform_wrapper.gravity-theme .gfield_error legend, .gform_wrapper.gravity-theme .gfield_error label, .gform_wrapper.gravity-theme .gfield_error .gfield_repeater_cell label, .gform_wrapper.gravity-theme .gfield_validation_message, .gform_wrapper.gravity-theme .validation_message {
		color: #c02b0a;
	}

.gform_wrapper.gravity-theme .gfield_validation_message, .gform_wrapper.gravity-theme .validation_message {
	background: #fff9f9;
	border: 1px solid #c02b0a;
	font-size: 14.992px;
	margin-top: 8px;
	padding: 12.992px 24px;
}

.gform_wrapper.gravity-theme .gfield_error [aria-invalid="true"] {
		border: 1px solid #c02b0a;
	}

/* Components */

/*
* Styles for form buttons.
*
* Used in the theme.css front-end stylesheet.
*/

.gform_wrapper.gravity-theme .gform_save_link.button, .gform_wrapper.gravity-theme .gform_previous_button.button {
		-webkit-appearance: none !important;
		background-color: #fff;
		color: #6b7280;
		text-decoration: none;
	}

.gform_wrapper.gravity-theme .gform_save_link.button svg, .gform_wrapper.gravity-theme .gform_previous_button.button svg {
			display: inline-block;
			margin-right: 4px;
		}

.gform_wrapper.gravity-theme button.button:disabled {
		opacity: 0.6;
	}

.gform_wrapper.gravity-theme #field_submit, .gform_wrapper.gravity-theme .gform_footer {
		display: flex;
	}

.gform_wrapper.gravity-theme #field_submit input, .gform_wrapper.gravity-theme .gform_footer input {
			align-self: flex-end;
		}

.gform_wrapper.gravity-theme #field_submit .gform_image_button, .gform_wrapper.gravity-theme .gform_footer .gform_image_button {
			background-color: transparent;
			border: none;
			border-radius: 0;
			height: auto;
			padding: 0;
			width: auto;
		}

.gform_wrapper.gravity-theme #field_submit .gform-button--width-full, .gform_wrapper.gravity-theme .gform_footer .gform-button--width-full {
			text-align: center;
			width: 100%;
		}

/*
* Styles for fields with multiple inputs.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_complex {
		display: flex;
		flex-flow: row wrap;
	}

.gform_wrapper.gravity-theme .ginput_complex span, .gform_wrapper.gravity-theme .ginput_complex fieldset {
			flex: 1;
		}

.gform_wrapper.gravity-theme .ginput_complex .ginput_full {
			flex: 0 0 100%;
		}

.gform_wrapper.gravity-theme .ginput_complex .clear-multi {
			display: flex;
		}

.gform_wrapper.gravity-theme .ginput_complex label, .gform_wrapper.gravity-theme .ginput_complex legend {
			display: block;
		}

.gform_wrapper.gravity-theme .ginput_complex input, .gform_wrapper.gravity-theme .ginput_complex select {
			width: 100%;
		}

.gform_wrapper.gravity-theme .ginput_container_address {
		margin-left: -1%;
		margin-right: -1%;
	}

.gform_wrapper.gravity-theme .ginput_container_address span {
			flex: 0 0 50%;
			padding-left: 0.9804%;
			padding-right: 0.9804%;
		}

.gform_wrapper.gravity-theme .gf_browser_ie .ginput_container_address span:not(.ginput_full) {
		flex: 0 0 49.3%;
	}

@media (min-width: 641px) {

		.gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full), .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) {
			padding-right: 1%;
		}

		.gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full), .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full) {
			padding-left: 1%;
			padding-right: 0;
		}

		/* Make sure all fields but the last one have a bottom margin so there's space between them */
		.gform_wrapper.gravity-theme .ginput_full:not(:last-of-type), .gform_wrapper.gravity-theme .ginput_container_address span {
			margin-bottom: 8px;
		}

		.gform_wrapper.gravity-theme .ginput_container_address {
			margin-bottom: -8px;
		}

			html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full), html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) {
				padding-left: 1%;
				padding-right: 0;
			}

			html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full), html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full) {
				padding-left: 0;
				padding-right: 1%;
			}

}

@media (max-width: 640px) {

	.gform_wrapper.gravity-theme .ginput_complex span {
		flex: 0 0 100%;
		margin-bottom: 8px;
		padding-left: 0;
	}

	.gform_wrapper.gravity-theme .ginput_complex.ginput_container_address span {
		padding-left: 0.9804%;
	}

}

/*
* Styles for field descriptions.
*
* Used in the theme.css front-end stylesheet.
*/

.gform_wrapper.gravity-theme .description, .gform_wrapper.gravity-theme .gfield_description, .gform_wrapper.gravity-theme .gsection_description, .gform_wrapper.gravity-theme .instruction {
		clear: both;
		font-family: inherit;
		font-size: 15px;
		letter-spacing: normal;
		line-height: inherit;
		padding-top: 13px;
		width: 100%;
	}

.gform_wrapper.gravity-theme .field_description_above .description, .gform_wrapper.gravity-theme .field_description_above .gfield_description, .gform_wrapper.gravity-theme .field_description_above .gsection_description, .gform_wrapper.gravity-theme .field_description_above .instruction {
			padding-bottom: 16px;
			padding-top: 0;
		}

.gform_wrapper.gravity-theme .field_sublabel_above .description, .gform_wrapper.gravity-theme .field_sublabel_above .gfield_description, .gform_wrapper.gravity-theme .field_sublabel_above .gsection_description {
			margin-top: 9.008px;
		}

.gform_wrapper.gravity-theme .top_label .gsection_description {
		margin-bottom: 9.008px;
		width: 100%;
	}

.gform_wrapper.gravity-theme .gfield_consent_description, .gform_wrapper.gravity-theme .field_description_below .gfield_consent_description {
		border: 1px solid #ddd;
		font-size: 13.008px;
		margin-top: 12.8px;
		max-height: 240px;
		overflow-y: scroll;
		padding: 6.4px 8px;
		width: 100%;
	}

/*
* Credit card field styles.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_creditcard_warning {
		background-color: rgba(255, 223, 224, 0.25);
		border-bottom: 4px solid #c02b0a;
		border-top: 2px solid #c02b0a;
		padding: 16px;
	}

.gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message {
			color: #c02b0a;
			font-family: inherit;
			font-size: 16px;
			font-weight: 700;
			min-height: 32px;
			position: relative;
		}

.gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message span {
				background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxOS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSItMTA2NyAyODY1IDI0IDMyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IC0xMDY3IDI4NjUgMjQgMzI7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiM3OTAwMDA7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik0tMTA1MywyODY1Yy01LjUsMC0xMCw0LjUtMTAsMTBoNGMwLTMuMywyLjctNiw2LTZjMy4zLDAsNiwyLjcsNiw2djJoLTIwdjE0YzAsMy4zLDIuNyw2LDYsNmgxMg0KCWMzLjMsMCw2LTIuNyw2LTZ2LTE2Qy0xMDQzLDI4NjkuNS0xMDQ3LjUsMjg2NS0xMDUzLDI4NjV6IE0tMTA0OSwyODkzaC0xMmMtMS4xLDAtMi0wLjktMi0ydi0xMGgxNnYxMA0KCUMtMTA0NywyODkyLjEtMTA0Ny45LDI4OTMtMTA0OSwyODkzeiBNLTEwNTMsMjg4N2MwLDEuMS0wLjksMi0yLDJzLTItMC45LTItMmMwLTEuMSwwLjktMiwyLTJTLTEwNTMsMjg4NS45LTEwNTMsMjg4N3oiLz4NCjwvc3ZnPg0K);
				background-position: 0 3.2px;
				background-repeat: no-repeat;
				background-size: 24px 16px;
				border-bottom: 1px solid #c02b0a;
				display: block;
				letter-spacing: 0.1pt;
				margin-bottom: 16px;
				padding: 0 0 16px 24px;
				text-transform: uppercase;
			}

.gform_wrapper.gravity-theme .ginput_cardextras {
		display: flex;
		flex-wrap: wrap;
	}

.gform_wrapper.gravity-theme .ginput_complex .ginput_cardinfo_right input {
		max-width: 112px;
		width: 30%;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_container {
		display: flex;
		flex-wrap: wrap;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_month {
		flex: 1;
		margin-right: 16px;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_year {
		flex: 1;
	}

.gform_wrapper.gravity-theme .ginput_cardinfo_right label, .gform_wrapper.gravity-theme .ginput_cardinfo_right legend, .gform_wrapper.gravity-theme .ginput_cardinfo_left label, .gform_wrapper.gravity-theme .ginput_cardinfo_left legend {
		flex: 1 1 100%;
	}

.gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left {
		display: flex;
		flex-direction: column;
	}

.gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left legend {
			float: left;
			padding-top: 5px;
		}

.gform_wrapper.gravity-theme .field_sublabel_below fieldset.ginput_cardinfo_left {
		flex-direction: column-reverse;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_card_expiration_month {
		margin-left: 16px;
		margin-right: 0;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gform_card_icon {
		float: right;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message span {
		background-position: right 3.2px;
		background-repeat: no-repeat;
		background-size: 24px 16px;
		display: block;
		margin-bottom: 16px;
		padding: 0 24px 16px 0;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left {
		display: flex;
	}

@media (max-width: 640px) {

	.gform_wrapper.gravity-theme .ginput_card_expiration {
		flex: none;
	}

	.gform_wrapper.gravity-theme .ginput_card_expiration_month {
		margin-bottom: 8px;
	}

}

/*
* Date field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_container_date + .ginput_container_date {
		margin-left: 2%;
	}

.gform_wrapper.gravity-theme .ginput_container_date[style*="display:none"] + .ginput_container_date {
		margin-left: 0;
	}

.gform_wrapper.gravity-theme .ginput_container_date label {
		display: block;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date + .ginput_container_date {
		margin-left: 0;
		margin-right: 16px;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date[style*="display:none"] + .ginput_container_date {
		margin-right: 0;
	}

/*
* Date field.
*
* Used in the basic.css front-end stylesheet and admin-theme.css.
*/

.gravity-theme.ui-datepicker {
	background: #fff;
	border: 1px solid #d0d1d3;
	border-radius: 3px;
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.11), 0 0 0.25rem rgba(18, 25, 97, 0.0405344);
	color: #607382;
	font-size: 0.8125rem;
	font-weight: var(--gform-theme-font-weight-medium);
	margin-top: 5px;
	padding: 18px 15px;
	width: auto;
}

.gravity-theme.ui-datepicker table, .gravity-theme.ui-datepicker thead, .gravity-theme.ui-datepicker tr, .gravity-theme.ui-datepicker td, .gravity-theme.ui-datepicker th {
		background: none;
		border: 0;
		margin: 0;
	}

.gravity-theme.ui-datepicker td, .gravity-theme.ui-datepicker th {
		padding: 5px;
		text-shadow: none;
		text-transform: none;
	}

.gravity-theme.ui-datepicker .ui-datepicker-header {
		border: 0;
		display: flex;
		flex-direction: row;
		justify-content: center;
		margin: 0 0 5px;
		padding: 0;
		position: relative;
		width: 100%;
	}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
			align-items: center;
			background: none;
			border: 0;
			color: #607382;
			cursor: pointer;
			display: flex;
			font-family: "gform-icons-theme" !important;
			font-size: 24px;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			height: 30px;
			justify-content: center;
			line-height: 1;
			position: absolute;
			speak: never;
			text-decoration: none;
			top: -2px;
			transition: color 300ms ease-in-out, background-color 300ms ease-in-out, border-color 300ms ease-in-out;
			width: 30px;
		}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:focus, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:hover, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next:focus, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next:hover {
				color: #2f4054;
			}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev .ui-icon, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next .ui-icon {
				display: none;
			}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev::before, .gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next::before {
				border: 0;
				height: auto;
				position: static;
				transform: none;
				width: auto;
			}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
			left: 0;
		}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-prev::before {
				content: "\e910";
			}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
			right: 0;
		}

.gravity-theme.ui-datepicker .ui-datepicker-header .ui-datepicker-next::before {
				content: "\e91b";
			}

.gravity-theme.ui-datepicker .ui-datepicker-header select {
			-webkit-appearance: none;
			background-color: transparent;
			background-image: url(../../../images/theme/down-arrow.svg);
			background-position: 100% 50%;
			background-repeat: no-repeat;
			background-size: 24px 24px;
			border: 0;
			border-radius: 0;
			box-shadow: none;
			color: #585e6a;
			cursor: pointer;
			display: inline-block;
			font-size: 0.875rem;
			font-weight: var(--gform-theme-font-weight-medium);
			height: auto;
			min-height: 0;
			padding: 0 22px 0 0;
			width: auto;
		}

.gravity-theme.ui-datepicker .ui-datepicker-header select.ui-datepicker-month {
				margin-right: 20px;
				-moz-text-align-last: right;
				     text-align-last: right;
			}

.gravity-theme.ui-datepicker .ui-datepicker-calendar span, .gravity-theme.ui-datepicker .ui-datepicker-calendar a {
			font-weight: var(--gform-theme-font-weight-normal);
		}

.gravity-theme.ui-datepicker .ui-datepicker-calendar th span {
				align-items: center;
				color: #2f4054;
				display: flex;
				font-size: 0.8125rem;
				font-weight: var(--gform-theme-font-weight-medium);
				height: 40px;
				justify-content: center;
				width: 40px;
			}

.gravity-theme.ui-datepicker .ui-datepicker-calendar td {
			font-size: 0.8125rem;
			height: 50px;
			width: 50px;
		}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-default {
			align-items: center;
			background: none;
			border: 1px solid transparent;
			border-radius: 100%;
			box-shadow: none;
			color: #2f4054;
			display: flex;
			height: 40px;
			justify-content: center;
			text-decoration: none;
			transition: color 300ms ease-in-out, background-color 300ms ease-in-out, border-color 300ms ease-in-out;
			width: 40px;
		}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-default:hover, .gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-default:focus {
				background: none;
				border-color: #607382;
				outline: none;
			}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-default:active {
				background: #f2f3f5;
				border-color: #607382;
			}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default {
				background: #607382;
				border-color: #607382;
				box-shadow: 0 0.125rem 0.125rem rgba(58, 58, 87, 0.0596411);
				color: #fff;
			}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default:hover, .gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default:focus {
					border-color: #607382;
				}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-disabled {
			background: none;
		}

.gravity-theme.ui-datepicker .ui-datepicker-calendar .ui-state-disabled .ui-state-default {
				align-items: center;
				background: #f2f3f5;
				border: 1px solid rgba(32, 32, 46, 0.079);
				border-radius: 100%;
				box-shadow: 0 0.125rem 0.125rem rgba(58, 58, 87, 0.0596411);
				color: #686e77;
				cursor: text;
				display: flex;
				height: 40px;
				justify-content: center;
				text-decoration: none;
				width: 40px;
			}

html[dir="rtl"] #ui-datepicker-div.gform-theme-datepicker[style] {
		right: auto !important;
	}

/*
* Form Footer.
*
* Used in theme.css and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gform_footer, .gform_wrapper.gravity-theme .gform_page_footer {
		margin: 6px 0 0;
		padding: 16px 0;
	}

.gform_wrapper.gravity-theme .gform_footer.right_label, .gform_wrapper.gravity-theme .gform_footer.left_label, .gform_wrapper.gravity-theme .gform_page_footer.right_label, .gform_wrapper.gravity-theme .gform_page_footer.left_label {
			padding: 16px 0 10px 30%;
		}

.gform_wrapper.gravity-theme .gform_footer input, .gform_wrapper.gravity-theme .gform_footer button, .gform_wrapper.gravity-theme .gform_page_footer input, .gform_wrapper.gravity-theme .gform_page_footer button {
			margin-bottom: 8px;
		}

.gform_wrapper.gravity-theme .gform_footer input.button:disabled, .gform_wrapper.gravity-theme .gform_footer button.button:disabled, .gform_wrapper.gravity-theme .gform_page_footer input.button:disabled, .gform_wrapper.gravity-theme .gform_page_footer button.button:disabled {
				opacity: 0.6;
			}

.gform_wrapper.gravity-theme .gform_footer button + input, .gform_wrapper.gravity-theme .gform_footer input + input, .gform_wrapper.gravity-theme .gform_footer input + button, .gform_wrapper.gravity-theme .gform_page_footer button + input, .gform_wrapper.gravity-theme .gform_page_footer input + input, .gform_wrapper.gravity-theme .gform_page_footer input + button {
			margin-left: 8px;
		}

html[dir="rtl"] .gform_wrapper.gravity-theme button + input, html[dir="rtl"] .gform_wrapper.gravity-theme input + input, html[dir="rtl"] .gform_wrapper.gravity-theme input + button {
			margin-right: 8px;
		}

/*
* Styles for basic inputs.
*
* Used in the basic.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_checkbox label, .gform_wrapper.gravity-theme .gfield_radio label {
			display: inline-block;
			font-size: 15px;
		}

.gform_wrapper.gravity-theme .gfield_checkbox button, .gform_wrapper.gravity-theme .gfield_checkbox input[type="text"], .gform_wrapper.gravity-theme .gfield_radio button, .gform_wrapper.gravity-theme .gfield_radio input[type="text"] {
			margin-top: 16px;
		}

.gform_wrapper.gravity-theme .gfield-choice-input {
		display: inline-block;
		margin-top: 0;
		top: 0;
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gfield-choice-input + label {
		margin-bottom: 0;
		max-width: calc(100% - 32px);
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gfield-choice-input:disabled + label {
		color: #757575;
	}

.gform_wrapper.gravity-theme input[type="number"] {
		display: inline-block;
	}

.gform_wrapper.gravity-theme input[type="text"], .gform_wrapper.gravity-theme input[type="password"], .gform_wrapper.gravity-theme input[type="email"], .gform_wrapper.gravity-theme input[type="url"], .gform_wrapper.gravity-theme input[type="date"], .gform_wrapper.gravity-theme input[type="month"], .gform_wrapper.gravity-theme input[type="time"], .gform_wrapper.gravity-theme input[type="datetime"], .gform_wrapper.gravity-theme input[type="datetime-local"], .gform_wrapper.gravity-theme input[type="week"], .gform_wrapper.gravity-theme input[type="number"], .gform_wrapper.gravity-theme input[type="search"], .gform_wrapper.gravity-theme input[type="tel"], .gform_wrapper.gravity-theme input[type="color"], .gform_wrapper.gravity-theme textarea, .gform_wrapper.gravity-theme select {
		font-size: 15px;
		margin-bottom: 0;
		margin-top: 0;
		padding: 8px;
	}

.gform_wrapper.gravity-theme .chosen-container-multi, .gform_wrapper.gravity-theme .ginput_product_price, .gform_wrapper.gravity-theme .ginput_product_price_label, .gform_wrapper.gravity-theme .ginput_quantity_label {
		font-size: 15px;
	}

.gform_wrapper.gravity-theme .chosen-choices {
		padding: 8px;
	}

.gform_wrapper.gravity-theme .ginput_container_date {
		align-content: flex-start;
		align-items: center;
		display: flex;
	}

.gform_wrapper.gravity-theme .ginput_container_date input {
			width: auto;
		}

.gform_wrapper.gravity-theme .ginput_container_date .datepicker_with_icon.large {
				width: calc(100% - 48px);
			}

.gform_wrapper.gravity-theme .ginput_container_date img.ui-datepicker-trigger {
			display: block;
			margin-left: 12.8px;
			max-height: 25.6px;
			max-width: 25.6px;
		}

.gform_wrapper.gravity-theme .ginput_complex .ginput_container_date {
		flex-basis: min-content;
		flex-flow: row wrap;
		max-width: 30%;

	}

.gform_wrapper.gravity-theme .ginput_complex .ginput_container_date input, .gform_wrapper.gravity-theme .ginput_complex .ginput_container_date select {
			min-width: 84px;
			width: 100%;
		}

.gform_wrapper.gravity-theme .gfield_chainedselect.horizontal select {
				min-width: 100px;
			}

.gform_wrapper.gravity-theme .gform_show_password {
		align-items: center;
		background: transparent;
		color: inherit;
		display: flex;
		height: 100%;
	}

.gform_wrapper.gravity-theme .gform_show_password:hover, .gform_wrapper.gravity-theme .gform_show_password:focus {
			background: transparent;
		}

.gform_wrapper.gravity-theme .gfield_consent_description {
		border: 1px solid #ddd;
		font-size: 0.8em;
		margin-top: 8px;
		max-height: 240px;
		overflow-y: scroll;
		padding: 8px;
	}

.gform_wrapper.gravity-theme .gfield .ginput_quantity {
		width: auto;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date img.ui-datepicker-trigger {
			margin-left: 0;
			margin-right: 12.8px;
			order: 1;
		}

.gform_wrapper.gravity-theme .gsection {
		border-bottom: 1px solid #ccc;
		padding: 0 16px 8px 0;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gsection {
			padding: 0 0 8px 16px;
		}

/*
* Progress bar.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gf_progressbar_title {
		color: #767676;
		font-size: 12.992px;
		font-weight: normal;
		margin: 0 0 4.8px 0;
	}

.gform_wrapper.gravity-theme .gf_progressbar {
		background: #e8e8e8;
		border-radius: 10.5px;
		margin-bottom: 16px;
		position: relative;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage {
		border-radius: 10.5px;
		height: 20.992px;
		text-align: right;
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage:not(.percentbar_100) {
		border-radius: 10.5px 0 0 10.5px;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage span {
		display: block;
		float: right;
		font-size: 13px;
		line-height: 21px;
		margin-left: 8px;
		margin-right: 8px;
		min-width: 2em;
		width: auto;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_0 span {
		color: #959595;
		float: none;
		margin-left: 9.6px;
		text-shadow: none;
	}

.gform_wrapper.gravity-theme .gf_progressbar_blue {
		background: #d2d6dc;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue {
		background-color: #1e7ac4;
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_gray {
		background: #d2d6dc;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_gray {
		background-color: #6b7280;
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_green {
		background: #e8e8e8;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_green {
		background-color: #aac138;
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_orange {
		background: #e8eaec;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_orange {
		background-color: #ff5d38;
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_red {
		background: #e8eaec;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_red {
		background-color: #ec1e31;
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_spring {
		background: #d2d6dc;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_spring {
		background: linear-gradient(270deg, #9cd790 0%, #76d7db 100%);
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_blues {
		background: #d2d6dc;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blues {
		background: linear-gradient(270deg, #00c2ff 0%, #7838e2 100%);
		color: #fff;
	}

.gform_wrapper.gravity-theme .gf_progressbar_rainbow {
		background: #d2d6dc;
	}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_rainbow {
		background: linear-gradient(270deg, #1dbeea 0%, #cd6ad6 50%, #f35160 100%);
		color: #fff;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gf_progressbar_percentage:not(.percentbar_100) {
	border-radius: 0 10.5px 10.5px 0;
}

/*
* Page steps.
*
* Used in the theme.css.
*/

.gform_wrapper.gravity-theme .gf_page_steps {
		border-bottom: 1px solid #e5e7eb;
		margin-bottom: 16px;
	}

.gform_wrapper.gravity-theme .gf_step {
		display: inline-block;
		margin: 8px 32px 8px 0;
	}

.gform_wrapper.gravity-theme .gf_step_number, .gform_wrapper.gravity-theme .gf_step_label {
		display: table-cell;
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gf_step_number {
		background: transparent;
		border: 2px solid #cfd3d9;
		border-radius: 20px;
		color: #585e6a;
		font-size: 14px;
		font-weight: 500;
		height: 40px;
		text-align: center;
		width: 40px;
	}

.gform_wrapper.gravity-theme .gf_step_active .gf_step_number {
		background: #cfd3d9;
		color: #607382;
	}

.gform_wrapper.gravity-theme .gf_step_completed .gf_step_number {
		position: relative;
	}

.gform_wrapper.gravity-theme .gf_step_completed .gf_step_number::before {
			background: #607382;
			border: 2px solid #607382;
			border-radius: 20px;
			content: "";
			display: table-cell;
			height: 40px;
			left: -2px;
			position: absolute;
			top: -2px;
			width: 40px;
		}

.gform_wrapper.gravity-theme .gf_step_completed .gf_step_number::after {
			align-items: center;
			color: #fff;
			content: "\e917";
			display: flex;
			font-family: "gform-icons-theme";
			font-size: 24px;
			height: 100%;
			justify-content: center;
			left: 0;
			position: absolute;
			text-align: center;
			top: 0;
			width: 100%;
			z-index: 5;
		}

.gform_wrapper.gravity-theme .gf_step_label {
		color: inherit;
		font-size: 14px;
		font-weight: 600;
		line-height: 16px;
		padding-left: 16px;
	}

.gform_wrapper.gravity-theme .gf_step_hidden {
		display: none;
	}

/*
* Pricing field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_product_price_wrapper {
		display: inline-block;
	}

.gform_wrapper.gravity-theme .ginput_product_price_wrapper input:-moz-read-only {
			background: none;
			border: none;
			padding: 0;
		}

.gform_wrapper.gravity-theme .ginput_product_price_wrapper input:read-only {
			background: none;
			border: none;
			padding: 0;
		}

.gform_wrapper.gravity-theme .ginput_product_price, .gform_wrapper.gravity-theme .ginput_shipping_price {
		color: #900;
	}

.gform_wrapper.gravity-theme .ginput_total {
		color: #060;
	}

/*
* Repeater field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_repeater_cell {
		margin-top: 8px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_cell label {
		color: rgb(155, 154, 154);
		font-size: 12.8px;
		font-weight: 400;
		padding-top: 8px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_items .gfield_repeater_cell:not(:first-child) {
		padding-top: 8px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_wrapper input {
		border: 1px solid rgba(197, 198, 197, 1);
		border-radius: 4px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_cell > .gfield_repeater_wrapper {
		background-color: rgba(1, 1, 1, 0.02);
		border-bottom: 1px solid rgba(238, 238, 238, 1);
		border-left: 8px solid rgba(241, 241, 241, 1);
		border-radius: 8px;
		box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06), 0 2px 1px -1px rgba(0, 0, 0, 0.06), 0 1px 5px 0 rgba(0, 0, 0, 0.06);
		padding: 10px 20px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_buttons {
		padding-top: 16px;
	}

.gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_plus:hover, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_minus:hover, .gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_text:hover, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_text:hover {
		background: rgba(250, 250, 250, 1);
		border: 1px solid rgba(117, 117, 117, 1);
		color: #374750;
	}

.gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_plus:focus, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_minus:focus, .gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_text:focus, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_text:focus {
		outline: 0;
	}

.gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_text, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_text {
		background: rgba(242, 242, 242, 0.5);
		border: 1px solid rgba(117, 117, 117, 0.4);
		border-radius: 20px;
		color: rgba(117, 117, 117, 1);
		font-size: 12px;
		font-weight: 400;
		height: 32px;
		margin-bottom: 8px;
		margin-right: 8px;
		min-width: 100px;
		transition: all 0.3s cubic-bezier(0.67, 0.17, 0.4, 0.83);
	}

.gform_wrapper.gravity-theme .gfield_repeater_buttons .add_repeater_item_plus, .gform_wrapper.gravity-theme .gfield_repeater_buttons .remove_repeater_item_minus {
		background: rgba(242, 242, 242, 0.5);
		border: 1px solid rgba(117, 117, 117, 0.4);
		border-radius: 50%;
		color: rgba(117, 117, 117, 1);
		font-size: 16px;
		font-weight: 700;
		height: 24px;
		margin: 10px 5px 0 5px;
		padding: 0;
		transition: all 0.3s cubic-bezier(0.67, 0.17, 0.4, 0.83);
		width: 24px;
	}

.gform_wrapper.gravity-theme .gfield_repeater .gfield_repeater_items .gfield_repeater_item:not(:last-child) {
		border-bottom: 2px solid #e0e0e6;
		margin-bottom: 20px;
		margin-right: 8px;
		padding-bottom: 20px;
	}

/*
* Styles for the time field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_time_hour label.hour_label, .gform_wrapper.gravity-theme .gfield_time_minute label.minute_label {
		display: block;
		font-size: 15px;
		margin: 0;
		padding-top: 5px;
	}

.gform_wrapper.gravity-theme .ginput_container_time {
		flex-basis: min-content;
		max-width: 64px;
		min-width: 64px;
	}

.gform_wrapper.gravity-theme .ginput_container_time input {
			margin-left: 0;
			margin-right: 0;
			min-width: 100%;
		}

.gform_wrapper.gravity-theme .gfield_time_minute {
		position: relative;
	}

.gform_wrapper.gravity-theme .hour_minute_colon {
		line-height: 2;
		padding: 0 8px;
	}

.gform_wrapper.gravity-theme .field_sublabel_above .hour_minute_colon {
		align-self: flex-end;
	}

.gform_wrapper.gravity-theme .gfield_time_ampm {
		align-items: flex-end;
		display: flex;
		margin-left: 16px;
	}

.gform_wrapper.gravity-theme .hour_minute_colon.below, .gform_wrapper.gravity-theme .gfield_time_ampm.below {
		align-items: flex-start;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_time_ampm {
			margin-left: 0;
			margin-right: 16px;
		}

/*# sourceMappingURL=theme.css.map */
