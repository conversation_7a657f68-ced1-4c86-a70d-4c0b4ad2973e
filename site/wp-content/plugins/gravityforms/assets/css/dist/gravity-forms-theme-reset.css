/*
----------------------------------------------------------------

gravity-forms-theme-reset.css
Gravity Forms Theme Reset
https://www.gravityforms.com

Gravity Forms is a Rocketgenius project
copyright 2008-2024 Rocketgenius Inc.
https://www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

/* stylelint-disable */

.gform-theme--framework {

	/***
	The new CSS reset - version 1.9 (last updated 19.6.2023)
	GitHub page: https://github.com/elad2412/the-new-css-reset
	***/
}

/*
	Remove all the styles of the "User-Agent-Stylesheet", except for the 'display' property
	- The "symbol *" part is to solve Firefox SVG sprite bug
	*/

.gform-theme--framework *:where(
		:not(html):not(iframe):not(canvas):not(img):not(svg):not(video)
		:not(svg *):not(symbol *)
		:not(.gform-theme__no-reset--el):not(.gform-theme__no-reset--children *):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-reset):not(.gform-theme__disable-reset *):not(.gform_heading *):not(.gfield--type-html *):not(.gfield--type-section *):not(.form_saved_message > *):not(.form_saved_message_sent > *):not(.gform_confirmation_message *):not(.wp-editor-container):not(.mce-tinymce):not(.mce-tinymce *):not(.wp-editor-area):not(.gfield_description > *):not(.gform-field-label--type-inline > *:not(span))
	) {
		all: unset;
		display: revert;
	}

/* Preferred box-sizing value */

.gform-theme--framework *, .gform-theme--framework *::before, .gform-theme--framework *::after {
		box-sizing: border-box;
	}

/* Reapply the pointer cursor for anchor tags */

.gform-theme--framework a, .gform-theme--framework button {
		cursor: revert;
	}

/* Remove list styles (bullets/numbers) */

.gform-theme--framework ol, .gform-theme--framework ul, .gform-theme--framework menu {
		list-style: none;
	}

/* For images to not be able to exceed their container */

.gform-theme--framework img {
		max-inline-size: 100%;
		max-block-size: 100%;
	}

/* removes spacing between cells in tables */

.gform-theme--framework table {
		border-collapse: collapse;
	}

/* Safari - solving issue when using user-select:none on the <body> text input doesn't working */

.gform-theme--framework input, .gform-theme--framework textarea {
		-webkit-user-select: auto;
	}

/* revert the 'white-space' property for textarea elements on Safari */

.gform-theme--framework textarea {
		white-space: revert;
	}

/* minimum style to allow to style meter element */

.gform-theme--framework meter {
		-webkit-appearance: revert;
		-moz-appearance: revert;
		     appearance: revert;
	}

/* preformatted text - use only for this feature */

.gform-theme--framework :where(pre) {
		all: revert;
	}

/* reset default text opacity of input placeholder */

.gform-theme--framework ::-moz-placeholder {
		color: unset;
	}

.gform-theme--framework ::placeholder {
		color: unset;
	}

/* fix the feature of 'hidden' attribute.
	   display:revert; revert to element instead of attribute */

.gform-theme--framework :where([hidden]) {
		display: none;
	}

/* revert for bug in Chromium browsers
	   - fix for the content editable attribute will work properly.
	   - webkit-user-select: auto; added for Safari in case of using user-select:none on wrapper element*/

.gform-theme--framework :where([contenteditable]:not([contenteditable="false"])) {
		-moz-user-modify: read-write;
		-webkit-user-modify: read-write;
		word-wrap: break-word;
		-webkit-line-break: after-white-space;
		-webkit-user-select: auto;
	}

/* apply back the draggable feature - exist only in Chromium and Safari */

.gform-theme--framework :where([draggable="true"]) {
		-webkit-user-drag: element;
	}

/* Revert Modal native behavior */

.gform-theme--framework :where(dialog:modal) {
		all: revert;
	}

/*
 * More targeted, theme based reset
 */

.gform-theme--framework input[type="radio"]::before, .gform-theme--framework input[type="checkbox"]::before {
			height: auto;
			margin-block: 0;
			margin-inline: 0;
			position: static;
			width: auto;
		}

.gform-theme--framework input[type="radio"]::after, .gform-theme--framework input[type="checkbox"]::after {
			content: none;
		}

/* stylelint-enable */

/*# sourceMappingURL=gravity-forms-theme-reset.css.map */
