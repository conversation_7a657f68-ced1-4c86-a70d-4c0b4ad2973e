.gform-theme--framework.gform_editor .ginput_preview_list{display:none!important}.gform-theme--framework.gform-editor--compact{--gf-ctrl-label-font-size-req:11px}.gform-theme--framework.gform_editor{--gf-field-img-choice-size:100%;--gf-field-img-choice-card-placeholder-bg-color:rgba(var(--gf-color-in-ctrl-light-rgb), 0.5);--gf-field-img-choice-shadow-hover:0 0 0 rgba(18, 25, 97, 0.05),0 2px 5px rgba(18, 25, 97, 0.1),0 1px 1px rgba(18, 25, 97, 0.15)}.gform-theme--framework.gform_editor .gfield--type-image_choice .gfield_checkbox,.gform-theme--framework.gform_editor .gfield--type-image_choice .gfield_radio{display:grid;grid-template-columns:repeat(4,1fr)}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)],.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gfield-choice-image-wrapper{--gf-local-shadow:var(--gf-field-img-choice-shadow)}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled),.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-no-card .gchoice:has(.gfield-choice-input:disabled) .gfield-choice-image-wrapper{--gf-local-shadow:var(--gf-field-img-choice-shadow)}.gform-theme--framework.gform_editor .gfield--type-image_choice .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gfield-choice-image-wrapper{opacity:1}.gform-theme--framework.gform_editor .gfield--type-image_choice .gchoice:has(.gfield-choice-input:disabled) .gfield-choice-image-wrapper{opacity:1}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)]{--gf-local-bg-color:var(--gf-ctrl-bg-color);--gf-local-border-color:var(--gf-color-in-ctrl-light-darker)}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled){--gf-local-bg-color:var(--gf-ctrl-bg-color);--gf-local-border-color:var(--gf-color-in-ctrl-light-darker)}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice[\:has\(.gfield-choice-input\:disabled\)] .gform-field-label{--gf-local-color:var(--gf-ctrl-color)}.gform-theme--framework.gform_editor .gfield--type-image_choice.gfield--image-choice-appearance-card .gchoice:has(.gfield-choice-input:disabled) .gform-field-label{--gf-local-color:var(--gf-ctrl-color)}.gform-theme--framework.gform_editor .gfield--input-type-datepicker.gfield--datepicker-custom-icon .ginput_container_date input{--gf-local-padding-x:var(--gf-ctrl-padding-x)}