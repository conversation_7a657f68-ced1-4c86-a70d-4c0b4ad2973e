/*
----------------------------------------------------------------

blocks.css
Gravity Forms Blocks Styles
http: //www.gravityforms.com

Gravity Forms is a Rocketgenius project
copyright 2008-2024 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

/* Blocks */

.gform-block__placeholder {
	padding: 2em;
}

.gform-block__placeholder .components-placeholder__fieldset, .gform-block__placeholder .components-placeholder__fieldset form {
		justify-content: center;
	}

.gform-block__placeholder select {
		min-width: 300px;
	}

.gform-block__placeholder-brand {
	text-align: center;
}

.gform-block__placeholder-brand p {
		font-family: inherit;
		margin: 0.5em 0;
	}

.gform-block__panel {
	border-top: 1px solid #e2e4e7 !important;
}

.gform-block__panel .block-editor-panel-color-gradient-settings__item-group {
		border: none;
		border-radius: 0;
	}

.gform-block__panel .block-editor-panel-color-gradient-settings__item-group div[class$="-child-palette"] {
			border-left: 1px solid rgba(0, 0, 0, 0.1);
			border-right: 1px solid rgba(0, 0, 0, 0.1);
			border-top: 1px solid rgba(0, 0, 0, 0.1);
			width: 100%;
		}

.gform-block__panel .gform-block-editor-panel__first-child-palette {
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
	}

.gform-block__panel .gform-block-editor-panel__last-child-palette {
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		border-bottom-left-radius: 2px;
		border-bottom-right-radius: 2px;
	}

.gform-block__components-flex--adjust-gap {
	gap: 8px;
}

.gform_wrapper .gform_fields, .gform_wrapper .gform_footer {
	pointer-events: none;
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
}

.gform-block__placeholder .gform-icon svg {
	fill: #82878c;
	width: 110px;
}

.gform-block__alert {
	margin-bottom: 2px;
}

.gform-block__alert p {
		font-size: 0.8125em;
		font-weight: 600;
		margin: 0 !important;
	}

.gform-block__alert.gform-block__alert-error {
		background: #fff0f0;
		color: #fc1d1d;
		padding: 0.75em 1em;
	}

.gform-block__toolbar-button {
	align-items: center;
	border-radius: 0;
	border-right: 1px solid #000;
	display: flex;
	height: 100%;
	justify-content: center;
	min-width: 48px;
	padding-left: 12px;
	padding-right: 12px;
}

.gform-block__toolbar-button i.gform-icon {
		font-style: normal;
	}

.components-accessible-toolbar .gform-block__toolbar-button {
		margin-top: -1px;
	}

.gform-block__form-styles .components-base-control {
		margin-bottom: 8px;
	}

.gform-block__form-styles .gform-block__theme-reset-defaults {
		display: block;
		text-align: center;
		width: 100%;
	}

.components-button.has-icon .dashicon.dashicon-gravityforms {
	margin: 0;
}

.edit-post-header__toolbar .gform-block__toolbar-button {
		border-right: 0;
		height: 100%;
		margin-left: 6px;
		padding-left: 6px;
		padding-right: 6px;
	}

.edit-post-header__toolbar .gform-block__toolbar-button + .gform-block__toolbar-button {
			margin-left: 0;
		}

.gform-block__toolbar-button i {
	font-size: 24px;
}

.gform-block__tooltip {
	color: #242748;
	font-family: inter, -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen-sans, ubuntu, cantarell, "Helvetica Neue", sans-serif;
	font-size: 13px;
	line-height: 19px;
	opacity: 1;
	position: fixed;
	text-align: center;
	transform: translateY(-50%);
	transition: opacity 200ms ease-in-out;
	width: 255px;
	z-index: 10000;
}

.gform-block__tooltip::before, .gform-block__tooltip::after {
		border-color: transparent #fff transparent transparent;
		border-style: solid;
		border-width: 14px 15px 14px 0;
		content: "";
		height: 0;
		left: 0;
		margin-top: -14px;
		position: absolute;
		top: 50%;
		width: 0;
	}

.gform-block__tooltip::before {
		filter: drop-shadow(0 2px 4px rgba(58, 61, 90, 0.15));
		z-index: 5;
	}

.gform-block__tooltip::after {
		left: 3px;
		transform: scale(1.5);
		z-index: 6;
	}

.gform-block__tooltip .gform-link {
		color: #3985b7;
		font-size: 13px;
		text-decoration: none;
	}

.gform-block__tooltip .gform-link:hover, .gform-block__tooltip .gform-link:focus {
			box-shadow: none;
			outline: none;
			text-decoration: underline;
		}

.gform-block__tooltip .gform-link::before {
			content: "";
			display: block;
		}

.gform-block__tooltip-inner {
	background: #fff;
	border-radius: 3px;
	box-shadow: 0 10px 15px rgba(58, 61, 90, 0.1), 0 4px 6px rgba(58, 61, 90, 0.05);
	margin-left: 15px;
	overflow: hidden;
	padding: 11px 30px;
	position: relative;
	width: 240px;
	z-index: 4;
}

.gform-block__tooltip-title {
	display: block;
	font-weight: 500;
	margin-bottom: 8px;
}

html[dir="rtl"] .gform-block__tooltip::before, html[dir="rtl"] .gform-block__tooltip::after {
			left: auto;
		}

html[dir="rtl"] .gform-block__tooltip::before {
			right: 0;
			transform: rotate(180deg);
		}

html[dir="rtl"] .gform-block__tooltip::after {
			right: 3px;
			transform: scale(1.5) rotate(180deg);
		}

html[dir="rtl"] .gform-block__tooltip-inner {
		margin: 0 15px 0 0;
	}

html[dir="rtl"] .gform-block__toolbar-button:first-child {
			border-right: 0;
		}

html[dir="rtl"] .gform-block__toolbar-button:last-child {
			border-left: 1px solid #000;
		}

/*# sourceMappingURL=blocks.css.map */
