/*
----------------------------------------------------------------

gravity-forms-common-icons.css
Gravity Forms Common Icon Kit
https://www.gravityforms.com

Gravity Forms is a Rocketgenius project
copyright 2008-2024 Rocketgenius Inc.
https://www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

/* stylelint-disable */

/* -----------------------------------------------------------------------------
 *
 * Common Font Icons (via IcoMoon)
 *
 * This file is generated using the `gulp icons` task. Do not edit it directly.
 *
 * ----------------------------------------------------------------------------- */

@font-face {
	font-family: 'gform-icons-common';
	src:	url('fonts/gform-icons-common.eot?x86y73');
	src:	url('../../../fonts/gform-icons-common.eot?x86y73#iefix') format('embedded-opentype'),
		url('../../../fonts/gform-icons-common.woff2?x86y73') format('woff2'),
		url('../../../fonts/gform-icons-common.ttf?x86y73') format('truetype'),
		url('../../../fonts/gform-icons-common.woff?x86y73') format('woff'),
		url('../../../fonts/gform-icons-common.svg?x86y73#gform-icons-common') format('svg');
	font-weight: normal;
	font-style: normal;
	font-display: block;
}

.gform-common-icon {
	/* use !important to prevent issues with browser extensions that change fonts */
	font-family: "gform-icons-common" !important;
	speak: never;
	font-style: normal;
	font-weight: normal;
	font-feature-settings: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.gform-common-icon--star:before {
	content: "\e90c";
}

.gform-common-icon--credit-card:before {
	content: "\e90a";
}

.gform-common-icon--exclamation-circle:before {
	content: "\e90b";
}

.gform-common-icon--check-circle:before {
	content: "\e902";
}

.gform-common-icon--exclamation:before {
	content: "\e910";
}

.gform-common-icon--information-circle:before {
	content: "\e911";
}

.gform-common-icon--eye:before {
	content: "\e909";
}

.gform-common-icon--pencil:before {
	content: "\e908";
}

.gform-common-icon--arrow-narrow-left:before {
	content: "\e900";
}

.gform-common-icon--arrow-narrow-right:before {
	content: "\e901";
}

.gform-common-icon--check-mark-alt:before {
	content: "\e903";
}

.gform-common-icon--check-mark:before {
	content: "\e904";
}

.gform-common-icon--chevron-down:before {
	content: "\e905";
}

.gform-common-icon--chevron-up:before {
	content: "\e906";
}

.gform-common-icon--x:before {
	content: "\e907";
}

.gform-common-icon--upload-file:before {
	content: "\e90e";
}

.gform-common-icon--play-arrow:before {
	content: "\e90d";
}

/*# sourceMappingURL=gravity-forms-common-icons.css.map */
